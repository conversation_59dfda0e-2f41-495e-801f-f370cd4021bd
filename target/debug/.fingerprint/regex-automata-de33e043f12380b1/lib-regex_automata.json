{"rustc": 4013192585442940105, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 15630646695703972922, "profile": 6707562999592697545, "path": 4176840966014752803, "deps": [[554324495028472449, "memchr", false, 5903607324069307402], [7325384046744447800, "aho_corasick", false, 2250035490103517014], [9111760993595911334, "regex_syntax", false, 16334784425213773517]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-automata-de33e043f12380b1/dep-lib-regex_automata"}}], "rustflags": [], "metadata": 8878122455581797878, "config": 2202906307356721367, "compile_kind": 0}