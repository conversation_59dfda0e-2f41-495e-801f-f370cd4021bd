{"rustc": 4013192585442940105, "features": "[\"any_impl\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 10676753825826352750, "profile": 6609184196851301694, "path": 13079493515799437826, "deps": [[416921746892697426, "crc32fast", false, 9203160219768276984], [8907076000657887192, "miniz_oxide", false, 16659022814738008164]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-5453e351194e281a/dep-lib-flate2"}}], "rustflags": [], "metadata": 1284714256429684901, "config": 2202906307356721367, "compile_kind": 0}