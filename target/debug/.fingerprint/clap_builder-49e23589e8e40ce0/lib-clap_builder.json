{"rustc": 4013192585442940105, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\", \"wrap_help\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4540639333657397710, "profile": 13557839117873329204, "path": 7204979855744802393, "deps": [[821897733253474908, "anstyle", false, 13911063121318686684], [967775003968733193, "strsim", false, 9935284355428059322], [2754101768631515696, "anstream", false, 17338977528109091126], [3140197793370367388, "clap_lex", false, 2454212579411573046], [11030198492748297329, "terminal_size", false, 11019975127815803271]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-49e23589e8e40ce0/dep-lib-clap_builder"}}], "rustflags": [], "metadata": 13636260659328210681, "config": 2202906307356721367, "compile_kind": 0}