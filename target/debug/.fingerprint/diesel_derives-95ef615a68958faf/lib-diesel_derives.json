{"rustc": 4013192585442940105, "features": "[\"32-column-tables\", \"default\", \"r2d2\", \"sqlite\", \"with-deprecated\"]", "declared_features": "[\"128-column-tables\", \"32-column-tables\", \"64-column-tables\", \"chrono\", \"default\", \"mysql\", \"nightly\", \"postgres\", \"r2d2\", \"sqlite\", \"time\", \"with-deprecated\", \"without-deprecated\"]", "target": 3145938285851219322, "profile": 6707562999592697545, "path": 14801308660487737199, "deps": [[4643558856552587913, "dsl_auto_type", false, 2780156426204462806], [11852780150458150307, "syn", false, 11877837821996802570], [13329370913566841603, "diesel_table_macro_syntax", false, 15466778047903362225], [17525013869477438691, "quote", false, 18266876090497343978], [18036439996138669183, "proc_macro2", false, 856274116545904419]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/diesel_derives-95ef615a68958faf/dep-lib-diesel_derives"}}], "rustflags": [], "metadata": 5456329413592821122, "config": 2202906307356721367, "compile_kind": 0}