{"rustc": 4013192585442940105, "features": "[]", "declared_features": "[\"nightly_derive\"]", "target": 10956379173884574485, "profile": 6707562999592697545, "path": 1867566604942598607, "deps": [[2452538001284770427, "cfg_if", false, 293350827990332600], [9357895591565833906, "build_script_build", false, 16994524495534728988], [11852780150458150307, "syn", false, 11877837821996802570], [17525013869477438691, "quote", false, 18266876090497343978], [18036439996138669183, "proc_macro2", false, 856274116545904419]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mockall_derive-e3376b8dc58e7915/dep-lib-mockall_derive"}}], "rustflags": [], "metadata": 4612127955369317849, "config": 2202906307356721367, "compile_kind": 0}