{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":4,"byte_end":29,"line_start":1,"line_end":1,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":0,"byte_end":31,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use std::thread::sleep;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `async_trait::async_trait`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":85,"byte_end":109,"line_start":5,"line_end":5,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"use async_trait::async_trait;","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":81,"byte_end":111,"line_start":5,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use async_trait::async_trait;","highlight_start":1,"highlight_end":30},{"text":"use log::{debug, info, LevelFilter};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `async_trait::async_trait`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:5:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse async_trait::async_trait;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":128,"byte_end":132,"line_start":6,"line_end":6,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"use log::{debug, info, LevelFilter};","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":126,"byte_end":132,"line_start":6,"line_end":6,"column_start":16,"column_end":22,"is_primary":true,"text":[{"text":"use log::{debug, info, LevelFilter};","highlight_start":16,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `info`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:6:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::{debug, info, LevelFilter};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserialize`, `Serialize`, and `Serializer`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":160,"byte_end":171,"line_start":7,"line_end":7,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize, Serializer};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":173,"byte_end":182,"line_start":7,"line_end":7,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize, Serializer};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":184,"byte_end":194,"line_start":7,"line_end":7,"column_start":37,"column_end":47,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize, Serializer};","highlight_start":37,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":148,"byte_end":197,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize, Serializer};","highlight_start":1,"highlight_end":49},{"text":"use serde_json::{to_string, Deserializer, Value};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Deserialize`, `Serialize`, and `Serializer`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:7:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize, Serializer};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserializer`, `Value`, and `to_string`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":214,"byte_end":223,"line_start":8,"line_end":8,"column_start":18,"column_end":27,"is_primary":true,"text":[{"text":"use serde_json::{to_string, Deserializer, Value};","highlight_start":18,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":225,"byte_end":237,"line_start":8,"line_end":8,"column_start":29,"column_end":41,"is_primary":true,"text":[{"text":"use serde_json::{to_string, Deserializer, Value};","highlight_start":29,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":239,"byte_end":244,"line_start":8,"line_end":8,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"use serde_json::{to_string, Deserializer, Value};","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":197,"byte_end":247,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde_json::{to_string, Deserializer, Value};","highlight_start":1,"highlight_end":50},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Deserializer`, `Value`, and `to_string`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:8:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json::{to_string, Deserializer, Value};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `request_rust::request::error::RequestError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":252,"byte_end":294,"line_start":10,"line_end":10,"column_start":5,"column_end":47,"is_primary":true,"text":[{"text":"use request_rust::request::error::RequestError;","highlight_start":5,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":248,"byte_end":296,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use request_rust::request::error::RequestError;","highlight_start":1,"highlight_end":48},{"text":"use request_rust::setting::request_setting::{AppNetworkEnv, RequestConfigManager};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `request_rust::request::error::RequestError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse request_rust::request::error::RequestError;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `request_rust::tools::request_tools::SerdeTools`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":383,"byte_end":429,"line_start":12,"line_end":12,"column_start":5,"column_end":51,"is_primary":true,"text":[{"text":"use request_rust::tools::request_tools::SerdeTools;","highlight_start":5,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":379,"byte_end":431,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use request_rust::tools::request_tools::SerdeTools;","highlight_start":1,"highlight_end":52},{"text":"use rust_storage::api::storage_manager::StorageManager;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `request_rust::tools::request_tools::SerdeTools`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse request_rust::tools::request_tools::SerdeTools;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_userdomain::features::flat::cross_platform::lib_userdomain_cross_platform`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":943,"byte_end":1021,"line_start":21,"line_end":21,"column_start":5,"column_end":83,"is_primary":true,"text":[{"text":"use rust_userdomain::features::flat::cross_platform::lib_userdomain_cross_platform;","highlight_start":5,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":939,"byte_end":1023,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::features::flat::cross_platform::lib_userdomain_cross_platform;","highlight_start":1,"highlight_end":84},{"text":"use rust_userdomain::models::family_args::{CreateFamilyRoomArgs, FamilyArgs};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_userdomain::features::flat::cross_platform::lib_userdomain_cross_platform`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::features::flat::cross_platform::lib_userdomain_cross_platform;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateFamilyRoomArgs` and `FamilyArgs`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1066,"byte_end":1086,"line_start":22,"line_end":22,"column_start":44,"column_end":64,"is_primary":true,"text":[{"text":"use rust_userdomain::models::family_args::{CreateFamilyRoomArgs, FamilyArgs};","highlight_start":44,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1088,"byte_end":1098,"line_start":22,"line_end":22,"column_start":66,"column_end":76,"is_primary":true,"text":[{"text":"use rust_userdomain::models::family_args::{CreateFamilyRoomArgs, FamilyArgs};","highlight_start":66,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1023,"byte_end":1101,"line_start":22,"line_end":23,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::models::family_args::{CreateFamilyRoomArgs, FamilyArgs};","highlight_start":1,"highlight_end":78},{"text":"use rust_userdomain::models::user_args::UserArgs;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateFamilyRoomArgs` and `FamilyArgs`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:22:44\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::models::family_args::{CreateFamilyRoomArgs, FamilyArgs};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_userdomain::models::virtual_member_args::VirtualMemberArgs`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1155,"byte_end":1218,"line_start":24,"line_end":24,"column_start":5,"column_end":68,"is_primary":true,"text":[{"text":"use rust_userdomain::models::virtual_member_args::VirtualMemberArgs;","highlight_start":5,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1151,"byte_end":1220,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::models::virtual_member_args::VirtualMemberArgs;","highlight_start":1,"highlight_end":69},{"text":"use rust_userdomain::server_apis::device_apis::device_list_api;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_userdomain::models::virtual_member_args::VirtualMemberArgs`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:24:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::models::virtual_member_args::VirtualMemberArgs;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_userdomain::server_apis::device_apis::device_list_api`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1224,"byte_end":1282,"line_start":25,"line_end":25,"column_start":5,"column_end":63,"is_primary":true,"text":[{"text":"use rust_userdomain::server_apis::device_apis::device_list_api;","highlight_start":5,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1220,"byte_end":1284,"line_start":25,"line_end":26,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::server_apis::device_apis::device_list_api;","highlight_start":1,"highlight_end":64},{"text":"use rust_userdomain::{api::error::UserDomainError, models::auth_data::AuthData};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_userdomain::server_apis::device_apis::device_list_api`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:25:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::server_apis::device_apis::device_list_api;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `api::error::UserDomainError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1306,"byte_end":1333,"line_start":26,"line_end":26,"column_start":23,"column_end":50,"is_primary":true,"text":[{"text":"use rust_userdomain::{api::error::UserDomainError, models::auth_data::AuthData};","highlight_start":23,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1306,"byte_end":1335,"line_start":26,"line_end":26,"column_start":23,"column_end":52,"is_primary":true,"text":[{"text":"use rust_userdomain::{api::error::UserDomainError, models::auth_data::AuthData};","highlight_start":23,"highlight_end":52}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1305,"byte_end":1306,"line_start":26,"line_end":26,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use rust_userdomain::{api::error::UserDomainError, models::auth_data::AuthData};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1362,"byte_end":1363,"line_start":26,"line_end":26,"column_start":79,"column_end":80,"is_primary":true,"text":[{"text":"use rust_userdomain::{api::error::UserDomainError, models::auth_data::AuthData};","highlight_start":79,"highlight_end":80}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `api::error::UserDomainError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:26:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::{api::error::UserDomainError, models::auth_data::AuthData};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Runnable` and `Strategy`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1398,"byte_end":1406,"line_start":27,"line_end":27,"column_start":34,"column_end":42,"is_primary":true,"text":[{"text":"use task_manager::task_manager::{Runnable, Strategy};","highlight_start":34,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1408,"byte_end":1416,"line_start":27,"line_end":27,"column_start":44,"column_end":52,"is_primary":true,"text":[{"text":"use task_manager::task_manager::{Runnable, Strategy};","highlight_start":44,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1365,"byte_end":1419,"line_start":27,"line_end":28,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use task_manager::task_manager::{Runnable, Strategy};","highlight_start":1,"highlight_end":54},{"text":"use UserDomainEvent::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Runnable` and `Strategy`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:27:34\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse task_manager::task_manager::{Runnable, Strategy};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UserDomainEvent::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1423,"byte_end":1441,"line_start":28,"line_end":28,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"use UserDomainEvent::*;","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1419,"byte_end":1443,"line_start":28,"line_end":29,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use UserDomainEvent::*;","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UserDomainEvent::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:28:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse UserDomainEvent::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `args`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":10969,"byte_end":10973,"line_start":274,"line_end":274,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let args = UserArgs {","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":10969,"byte_end":10973,"line_start":274,"line_end":274,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let args = UserArgs {","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_args","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `args`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:274:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let args = UserArgs {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_args`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `image_url` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1450,"byte_end":1459,"line_start":30,"line_end":30,"column_start":7,"column_end":16,"is_primary":true,"text":[{"text":"const image_url: &str =","highlight_start":7,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `image_url` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:30:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst image_url: &str =\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `image_url` should have an upper case name","code":{"code":"non_upper_case_globals","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1450,"byte_end":1459,"line_start":30,"line_end":30,"column_start":7,"column_end":16,"is_primary":true,"text":[{"text":"const image_url: &str =","highlight_start":7,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_upper_case_globals)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to upper case","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs","byte_start":1450,"byte_end":1459,"line_start":30,"line_end":30,"column_start":7,"column_end":16,"is_primary":true,"text":[{"text":"const image_url: &str =","highlight_start":7,"highlight_end":16}],"label":null,"suggested_replacement":"IMAGE_URL","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `image_url` should have an upper case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/domain_test/user_domain_test.rs:30:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst image_url: &str =\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to upper case: `IMAGE_URL`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(non_upper_case_globals)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"17 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 17 warnings emitted\u001b[0m\n\n"}
