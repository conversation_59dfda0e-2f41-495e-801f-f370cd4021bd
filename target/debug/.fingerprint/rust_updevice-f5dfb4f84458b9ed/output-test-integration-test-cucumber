{"$message_type":"diagnostic","message":"unused import: `logic_engine::device::device_attribute::UpDeviceAttribute`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":277,"byte_end":334,"line_start":7,"line_end":7,"column_start":5,"column_end":62,"is_primary":true,"text":[{"text":"use logic_engine::device::device_attribute::UpDeviceAttribute;","highlight_start":5,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":273,"byte_end":336,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device::device_attribute::UpDeviceAttribute;","highlight_start":1,"highlight_end":63},{"text":"use logic_engine::device::device_caution::UpDeviceCaution;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::device::device_attribute::UpDeviceAttribute`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device::device_attribute::UpDeviceAttribute;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `logic_engine::device::device_caution::UpDeviceCaution`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":340,"byte_end":393,"line_start":8,"line_end":8,"column_start":5,"column_end":58,"is_primary":true,"text":[{"text":"use logic_engine::device::device_caution::UpDeviceCaution;","highlight_start":5,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":336,"byte_end":395,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device::device_caution::UpDeviceCaution;","highlight_start":1,"highlight_end":59},{"text":"use rust_updevice::api::device_filter::UpDeviceFilter;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::device::device_caution::UpDeviceCaution`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device::device_caution::UpDeviceCaution;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `convert_attribute_from` and `convert_caution_from`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":1423,"byte_end":1445,"line_start":25,"line_end":25,"column_start":41,"column_end":63,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":41,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":1447,"byte_end":1467,"line_start":25,"line_end":25,"column_start":65,"column_end":85,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":65,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":1383,"byte_end":1470,"line_start":25,"line_end":26,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":1,"highlight_end":87},{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{uhsd_usr_dev_connect_state_e, uhsd_usr_dev_online_state_e, uhsd_usr_device_bind_state_e, uhsd_usr_device_offline_cause_e, uhsd_usr_device_sleep_state_e, DeviceInfo};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `convert_attribute_from` and `convert_caution_from`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:25:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::models::device_connect_state::UpDeviceConnectState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1045,"byte_end":1110,"line_start":19,"line_end":19,"column_start":5,"column_end":70,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_connect_state::UpDeviceConnectState;","highlight_start":5,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1041,"byte_end":1112,"line_start":19,"line_end":20,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_connect_state::UpDeviceConnectState;","highlight_start":1,"highlight_end":71},{"text":"use rust_updevice::models::device_info::UpDeviceInfo;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::models::device_connect_state::UpDeviceConnectState`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::models::device_connect_state::UpDeviceConnectState;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::models::device_offline_cause::UpDeviceOfflineCause`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1170,"byte_end":1235,"line_start":21,"line_end":21,"column_start":5,"column_end":70,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_offline_cause::UpDeviceOfflineCause;","highlight_start":5,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1166,"byte_end":1237,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_offline_cause::UpDeviceOfflineCause;","highlight_start":1,"highlight_end":71},{"text":"use rust_updevice::models::device_online_state::UpDeviceOnlineState;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::models::device_offline_cause::UpDeviceOfflineCause`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::models::device_offline_cause::UpDeviceOfflineCause;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::models::device_online_state::UpDeviceOnlineState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1241,"byte_end":1304,"line_start":22,"line_end":22,"column_start":5,"column_end":68,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_online_state::UpDeviceOnlineState;","highlight_start":5,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1237,"byte_end":1306,"line_start":22,"line_end":23,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_online_state::UpDeviceOnlineState;","highlight_start":1,"highlight_end":69},{"text":"use rust_updevice::models::device_sleep_state::UpDeviceSleepState;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::models::device_online_state::UpDeviceOnlineState`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:22:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::models::device_online_state::UpDeviceOnlineState;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::models::device_sleep_state::UpDeviceSleepState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1310,"byte_end":1371,"line_start":23,"line_end":23,"column_start":5,"column_end":66,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_sleep_state::UpDeviceSleepState;","highlight_start":5,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1306,"byte_end":1373,"line_start":23,"line_end":24,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_sleep_state::UpDeviceSleepState;","highlight_start":1,"highlight_end":67},{"text":"use rust_updevice::utils::convert_vec;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::models::device_sleep_state::UpDeviceSleepState`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:23:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::models::device_sleep_state::UpDeviceSleepState;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::utils::convert_vec`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1377,"byte_end":1410,"line_start":24,"line_end":24,"column_start":5,"column_end":38,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec;","highlight_start":5,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1373,"byte_end":1412,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec;","highlight_start":1,"highlight_end":39},{"text":"use rust_updevice::utils::fn_clone::FnClone;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::utils::convert_vec`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:24:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::utils::convert_vec;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::str::FromStr`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1516,"byte_end":1533,"line_start":27,"line_end":27,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"use std::str::FromStr;","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":1512,"byte_end":1535,"line_start":27,"line_end":28,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::str::FromStr;","highlight_start":1,"highlight_end":23},{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::str::FromStr`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:27:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::str::FromStr;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary braces around method argument","code":{"code":"unused_braces","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29354,"byte_end":29356,"line_start":722,"line_end":722,"column_start":53,"column_end":55,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":53,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29364,"byte_end":29366,"line_start":722,"line_end":722,"column_start":63,"column_end":65,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":63,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_braces)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove these braces","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29354,"byte_end":29356,"line_start":722,"line_end":722,"column_start":53,"column_end":55,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":53,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29364,"byte_end":29366,"line_start":722,"line_end":722,"column_start":63,"column_end":65,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":63,"highlight_end":65}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unnecessary braces around method argument\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/engine_device_steps.rs:722:53\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m722\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_braces)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these braces\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m722\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map(\u001b[0m\u001b[0m\u001b[38;5;9m{ \u001b[0m\u001b[0mArc::new\u001b[0m\u001b[0m\u001b[38;5;9m }\u001b[0m\u001b[0m).collect();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m722\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map(Arc::new).collect();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary braces around method argument","code":{"code":"unused_braces","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29783,"byte_end":29785,"line_start":730,"line_end":730,"column_start":53,"column_end":55,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":53,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29793,"byte_end":29795,"line_start":730,"line_end":730,"column_start":63,"column_end":65,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":63,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these braces","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29783,"byte_end":29785,"line_start":730,"line_end":730,"column_start":53,"column_end":55,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":53,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29793,"byte_end":29795,"line_start":730,"line_end":730,"column_start":63,"column_end":65,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":63,"highlight_end":65}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unnecessary braces around method argument\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/engine_device_steps.rs:730:53\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these braces\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map(\u001b[0m\u001b[0m\u001b[38;5;9m{ \u001b[0m\u001b[0mArc::new\u001b[0m\u001b[0m\u001b[38;5;9m }\u001b[0m\u001b[0m).collect();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map(Arc::new).collect();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `cucumber::gherkin::Step`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/toolkit_steps.rs","byte_start":33,"byte_end":56,"line_start":2,"line_end":2,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use cucumber::gherkin::Step;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/toolkit_steps.rs","byte_start":29,"byte_end":58,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use cucumber::gherkin::Step;","highlight_start":1,"highlight_end":29},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `cucumber::gherkin::Step`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/toolkit_steps.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::gherkin::Step;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `convert_attribute_from` and `convert_caution_from`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":849,"byte_end":871,"line_start":18,"line_end":18,"column_start":41,"column_end":63,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":41,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":873,"byte_end":893,"line_start":18,"line_end":18,"column_start":65,"column_end":85,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":65,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":809,"byte_end":896,"line_start":18,"line_end":19,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":1,"highlight_end":87},{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `convert_attribute_from` and `convert_caution_from`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:18:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UhsdPairList`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":951,"byte_end":963,"line_start":19,"line_end":19,"column_start":56,"column_end":68,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};","highlight_start":56,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":949,"byte_end":963,"line_start":19,"line_end":19,"column_start":54,"column_end":68,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};","highlight_start":54,"highlight_end":68}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":940,"byte_end":941,"line_start":19,"line_end":19,"column_start":45,"column_end":46,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};","highlight_start":45,"highlight_end":46}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":963,"byte_end":964,"line_start":19,"line_end":19,"column_start":68,"column_end":69,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};","highlight_start":68,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UhsdPairList`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:19:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `StepUtils`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":164,"byte_end":173,"line_start":4,"line_end":4,"column_start":48,"column_end":57,"is_primary":true,"text":[{"text":"use crate::utils::step_utils::{get_step_utils, StepUtils};","highlight_start":48,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":162,"byte_end":173,"line_start":4,"line_end":4,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"use crate::utils::step_utils::{get_step_utils, StepUtils};","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":147,"byte_end":148,"line_start":4,"line_end":4,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"use crate::utils::step_utils::{get_step_utils, StepUtils};","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":173,"byte_end":174,"line_start":4,"line_end":4,"column_start":57,"column_end":58,"is_primary":true,"text":[{"text":"use crate::utils::step_utils::{get_step_utils, StepUtils};","highlight_start":57,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `StepUtils`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:4:48\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::utils::step_utils::{get_step_utils, StepUtils};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `libc::stat`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":264,"byte_end":274,"line_start":8,"line_end":8,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use libc::stat;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":260,"byte_end":276,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use libc::stat;","highlight_start":1,"highlight_end":16},{"text":"use logic_engine::device::command::Command;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `libc::stat`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse libc::stat;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `logic_engine::device::command::Command`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":280,"byte_end":318,"line_start":9,"line_end":9,"column_start":5,"column_end":43,"is_primary":true,"text":[{"text":"use logic_engine::device::command::Command;","highlight_start":5,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":276,"byte_end":320,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device::command::Command;","highlight_start":1,"highlight_end":44},{"text":"use logic_engine::device::device_attribute::UpDeviceAttribute;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::device::command::Command`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device::command::Command;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `logic_engine::device::device_caution::UpDeviceCaution`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":387,"byte_end":440,"line_start":11,"line_end":11,"column_start":5,"column_end":58,"is_primary":true,"text":[{"text":"use logic_engine::device::device_caution::UpDeviceCaution;","highlight_start":5,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":383,"byte_end":442,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device::device_caution::UpDeviceCaution;","highlight_start":1,"highlight_end":59},{"text":"use logic_engine::device::device_command::UpDeviceCommand;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::device::device_caution::UpDeviceCaution`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device::device_caution::UpDeviceCaution;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `command` and `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":528,"byte_end":532,"line_start":13,"line_end":13,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use logic_engine::device::{self, command};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":534,"byte_end":541,"line_start":13,"line_end":13,"column_start":34,"column_end":41,"is_primary":true,"text":[{"text":"use logic_engine::device::{self, command};","highlight_start":34,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":501,"byte_end":544,"line_start":13,"line_end":14,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device::{self, command};","highlight_start":1,"highlight_end":43},{"text":"use logic_engine::device_config::alarm;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `command` and `self`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:13:28\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device::{self, command};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `logic_engine::device_config::alarm`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":548,"byte_end":582,"line_start":14,"line_end":14,"column_start":5,"column_end":39,"is_primary":true,"text":[{"text":"use logic_engine::device_config::alarm;","highlight_start":5,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":544,"byte_end":584,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device_config::alarm;","highlight_start":1,"highlight_end":40},{"text":"use logic_engine::engine::modifier::trigger::alarm_trigger;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::device_config::alarm`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device_config::alarm;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `logic_engine::engine::modifier::trigger::alarm_trigger`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":588,"byte_end":642,"line_start":15,"line_end":15,"column_start":5,"column_end":59,"is_primary":true,"text":[{"text":"use logic_engine::engine::modifier::trigger::alarm_trigger;","highlight_start":5,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":584,"byte_end":644,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::engine::modifier::trigger::alarm_trigger;","highlight_start":1,"highlight_end":60},{"text":"use once_cell::sync::Lazy;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::engine::modifier::trigger::alarm_trigger`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::engine::modifier::trigger::alarm_trigger;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::api::device_injection::UpDeviceInjection`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":675,"byte_end":730,"line_start":17,"line_end":17,"column_start":5,"column_end":60,"is_primary":true,"text":[{"text":"use rust_updevice::api::device_injection::UpDeviceInjection;","highlight_start":5,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":671,"byte_end":732,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::api::device_injection::UpDeviceInjection;","highlight_start":1,"highlight_end":61},{"text":"use rust_updevice::api::error::DeviceError;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::api::device_injection::UpDeviceInjection`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::api::device_injection::UpDeviceInjection;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::models::device_connect_state::UpDeviceConnectState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":828,"byte_end":893,"line_start":20,"line_end":20,"column_start":5,"column_end":70,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_connect_state::UpDeviceConnectState;","highlight_start":5,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":824,"byte_end":895,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_connect_state::UpDeviceConnectState;","highlight_start":1,"highlight_end":71},{"text":"use rust_updevice::models::device_online_state::UpDeviceOnlineState;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::models::device_connect_state::UpDeviceConnectState`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:20:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::models::device_connect_state::UpDeviceConnectState;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_usdk::toolkit_ffi::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1128,"byte_end":1153,"line_start":24,"line_end":24,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::*;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1124,"byte_end":1155,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::*;","highlight_start":1,"highlight_end":31},{"text":"static DEVICE_TOOLKIT: Lazy<DeviceToolkit> = Lazy::new(|| DeviceToolkit::new());","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_usdk::toolkit_ffi::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:24:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_usdk::toolkit_ffi::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UhsdPairList`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1583,"byte_end":1595,"line_start":33,"line_end":33,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    UhsdPairList,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1577,"byte_end":1595,"line_start":32,"line_end":33,"column_start":97,"column_end":17,"is_primary":true,"text":[{"text":"    DeviceConnectState, DeviceCtrlRoute, DeviceFeedback, DeviceInfo, DeviceOnlineState, UhsdPair,","highlight_start":97,"highlight_end":98},{"text":"    UhsdPairList,","highlight_start":1,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UhsdPairList`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:33:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    UhsdPairList,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `when`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/voice_box_device_steps.rs","byte_start":43,"byte_end":47,"line_start":1,"line_end":1,"column_start":44,"column_end":48,"is_primary":true,"text":[{"text":"use cucumber::{gherkin::Step, given, then, when};","highlight_start":44,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/voice_box_device_steps.rs","byte_start":41,"byte_end":47,"line_start":1,"line_end":1,"column_start":42,"column_end":48,"is_primary":true,"text":[{"text":"use cucumber::{gherkin::Step, given, then, when};","highlight_start":42,"highlight_end":48}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `when`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/voice_box_device_steps.rs:1:44\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::{gherkin::Step, given, then, when};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot apply unary operator `!` to type `&dyn rust_updevice::device::extend_api::ExtendApi`","code":{"code":"E0600","explanation":"An unary operator was used on a type which doesn't implement it.\n\nErroneous code example:\n\n```compile_fail,E0600\nenum Question {\n    Yes,\n    No,\n}\n\n!Question::Yes; // error: cannot apply unary operator `!` to type `Question`\n```\n\nIn this case, `Question` would need to implement the `std::ops::Not` trait in\norder to be able to use `!` on it. Let's implement it:\n\n```\nuse std::ops::Not;\n\nenum Question {\n    Yes,\n    No,\n}\n\n// We implement the `Not` trait on the enum.\nimpl Not for Question {\n    type Output = bool;\n\n    fn not(self) -> bool {\n        match self {\n            Question::Yes => false, // If the `Answer` is `Yes`, then it\n                                    // returns false.\n            Question::No => true, // And here we do the opposite.\n        }\n    }\n}\n\nassert_eq!(!Question::Yes, false);\nassert_eq!(!Question::No, true);\n```\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":2095,"byte_end":2138,"line_start":41,"line_end":41,"column_start":13,"column_end":56,"is_primary":true,"text":[{"text":"    assert!(!aggregate_device.get_extend_api().as_ref() as *const _ as usize == 0);","highlight_start":13,"highlight_end":56}],"label":"cannot apply unary operator `!`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0600]\u001b[0m\u001b[0m\u001b[1m: cannot apply unary operator `!` to type `&dyn rust_updevice::device::extend_api::ExtendApi`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:41:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    assert!(!aggregate_device.get_extend_api().as_ref() as *const _ as usize == 0);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot apply unary operator `!`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot apply unary operator `!` to type `&dyn rust_updevice::device::extend_api::ExtendApi`","code":{"code":"E0600","explanation":"An unary operator was used on a type which doesn't implement it.\n\nErroneous code example:\n\n```compile_fail,E0600\nenum Question {\n    Yes,\n    No,\n}\n\n!Question::Yes; // error: cannot apply unary operator `!` to type `Question`\n```\n\nIn this case, `Question` would need to implement the `std::ops::Not` trait in\norder to be able to use `!` on it. Let's implement it:\n\n```\nuse std::ops::Not;\n\nenum Question {\n    Yes,\n    No,\n}\n\n// We implement the `Not` trait on the enum.\nimpl Not for Question {\n    type Output = bool;\n\n    fn not(self) -> bool {\n        match self {\n            Question::Yes => false, // If the `Answer` is `Yes`, then it\n                                    // returns false.\n            Question::No => true, // And here we do the opposite.\n        }\n    }\n}\n\nassert_eq!(!Question::Yes, false);\nassert_eq!(!Question::No, true);\n```\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":2847,"byte_end":2887,"line_start":60,"line_end":60,"column_start":13,"column_end":53,"is_primary":true,"text":[{"text":"    assert!(!common_device.get_extend_api().as_ref() as *const _ as usize == 0);","highlight_start":13,"highlight_end":53}],"label":"cannot apply unary operator `!`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0600]\u001b[0m\u001b[0m\u001b[1m: cannot apply unary operator `!` to type `&dyn rust_updevice::device::extend_api::ExtendApi`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:60:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    assert!(!common_device.get_extend_api().as_ref() as *const _ as usize == 0);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot apply unary operator `!`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot apply unary operator `!` to type `&dyn rust_updevice::device::extend_api::ExtendApi`","code":{"code":"E0600","explanation":"An unary operator was used on a type which doesn't implement it.\n\nErroneous code example:\n\n```compile_fail,E0600\nenum Question {\n    Yes,\n    No,\n}\n\n!Question::Yes; // error: cannot apply unary operator `!` to type `Question`\n```\n\nIn this case, `Question` would need to implement the `std::ops::Not` trait in\norder to be able to use `!` on it. Let's implement it:\n\n```\nuse std::ops::Not;\n\nenum Question {\n    Yes,\n    No,\n}\n\n// We implement the `Not` trait on the enum.\nimpl Not for Question {\n    type Output = bool;\n\n    fn not(self) -> bool {\n        match self {\n            Question::Yes => false, // If the `Answer` is `Yes`, then it\n                                    // returns false.\n            Question::No => true, // And here we do the opposite.\n        }\n    }\n}\n\nassert_eq!(!Question::Yes, false);\nassert_eq!(!Question::No, true);\n```\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":3627,"byte_end":3668,"line_start":79,"line_end":79,"column_start":13,"column_end":54,"is_primary":true,"text":[{"text":"    assert!(!washing_device.get_extend_api().as_ref() as *const _ as usize == 0);","highlight_start":13,"highlight_end":54}],"label":"cannot apply unary operator `!`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0600]\u001b[0m\u001b[0m\u001b[1m: cannot apply unary operator `!` to type `&dyn rust_updevice::device::extend_api::ExtendApi`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:79:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    assert!(!washing_device.get_extend_api().as_ref() as *const _ as usize == 0);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot apply unary operator `!`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `clear`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":20613,"byte_end":20618,"line_start":483,"line_end":483,"column_start":73,"column_end":78,"is_primary":true,"text":[{"text":"async fn execute_operate_command(_world: &mut MyWorld, command: String, clear: String) {","highlight_start":73,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":20613,"byte_end":20618,"line_start":483,"line_end":483,"column_start":73,"column_end":78,"is_primary":true,"text":[{"text":"async fn execute_operate_command(_world: &mut MyWorld, command: String, clear: String) {","highlight_start":73,"highlight_end":78}],"label":null,"suggested_replacement":"_clear","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `clear`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/engine_device_steps.rs:483:73\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m483\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn execute_operate_command(_world: &mut MyWorld, command: String, clear: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_clear`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 12 arguments but 10 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6013,"byte_end":6042,"line_start":132,"line_end":132,"column_start":9,"column_end":38,"is_primary":false,"text":[{"text":"        \"fake_device_id2\".to_string(),","highlight_start":9,"highlight_end":38}],"label":"expected `UpDeviceBasic`, found `String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6052,"byte_end":6057,"line_start":133,"line_end":133,"column_start":9,"column_end":14,"is_primary":false,"text":[{"text":"        extra,","highlight_start":9,"highlight_end":14}],"label":"expected `UpDevicePermission`, found `String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":5695,"byte_end":6064,"line_start":123,"line_end":134,"column_start":22,"column_end":6,"is_primary":false,"text":[{"text":"    UpDeviceInfo::new(","highlight_start":22,"highlight_end":23},{"text":"        \"haier-usdk\".to_string(),","highlight_start":1,"highlight_end":34},{"text":"        device_id.to_string(),","highlight_start":1,"highlight_end":31},{"text":"        Some(\"fake_type_id1\".to_string()),","highlight_start":1,"highlight_end":43},{"text":"        \"fake_type_name1\".to_string(),","highlight_start":1,"highlight_end":39},{"text":"        Some(\"fake_type_code1\".to_string()),","highlight_start":1,"highlight_end":45},{"text":"        \"fake_model1\".to_string(),","highlight_start":1,"highlight_end":35},{"text":"        \"fake_pro_no1\".to_string(),","highlight_start":1,"highlight_end":36},{"text":"        Some(\"fake_parent_id1\".to_string()),","highlight_start":1,"highlight_end":45},{"text":"        \"fake_device_id2\".to_string(),","highlight_start":1,"highlight_end":39},{"text":"        extra,","highlight_start":1,"highlight_end":15},{"text":"    )","highlight_start":1,"highlight_end":6}],"label":"two arguments of type `UpDeviceProduct` and `UpDeviceRelation` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":5813,"byte_end":5842,"line_start":127,"line_end":127,"column_start":9,"column_end":38,"is_primary":false,"text":[{"text":"        \"fake_type_name1\".to_string(),","highlight_start":9,"highlight_end":38}],"label":"expected `Option<std::string::String>`, found `std::string::String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":5852,"byte_end":5887,"line_start":128,"line_end":128,"column_start":9,"column_end":44,"is_primary":false,"text":[{"text":"        Some(\"fake_type_code1\".to_string()),","highlight_start":9,"highlight_end":44}],"label":"expected `std::string::String`, found `Option<std::string::String>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":5932,"byte_end":5958,"line_start":130,"line_end":130,"column_start":9,"column_end":35,"is_primary":false,"text":[{"text":"        \"fake_pro_no1\".to_string(),","highlight_start":9,"highlight_end":35}],"label":"expected `Option<std::string::String>`, found `std::string::String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":5968,"byte_end":6003,"line_start":131,"line_end":131,"column_start":9,"column_end":44,"is_primary":false,"text":[{"text":"        Some(\"fake_parent_id1\".to_string()),","highlight_start":9,"highlight_end":44}],"label":"expected `std::string::String`, found `Option<std::string::String>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":5678,"byte_end":5695,"line_start":123,"line_end":123,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    UpDeviceInfo::new(","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/repo_Uplus/updevice_rust/rust_updevice/src/models/device_info.rs","byte_start":922,"byte_end":925,"line_start":27,"line_end":27,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"did you mean","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":5695,"byte_end":6064,"line_start":123,"line_end":134,"column_start":22,"column_end":6,"is_primary":true,"text":[{"text":"    UpDeviceInfo::new(","highlight_start":22,"highlight_end":23},{"text":"        \"haier-usdk\".to_string(),","highlight_start":1,"highlight_end":34},{"text":"        device_id.to_string(),","highlight_start":1,"highlight_end":31},{"text":"        Some(\"fake_type_id1\".to_string()),","highlight_start":1,"highlight_end":43},{"text":"        \"fake_type_name1\".to_string(),","highlight_start":1,"highlight_end":39},{"text":"        Some(\"fake_type_code1\".to_string()),","highlight_start":1,"highlight_end":45},{"text":"        \"fake_model1\".to_string(),","highlight_start":1,"highlight_end":35},{"text":"        \"fake_pro_no1\".to_string(),","highlight_start":1,"highlight_end":36},{"text":"        Some(\"fake_parent_id1\".to_string()),","highlight_start":1,"highlight_end":45},{"text":"        \"fake_device_id2\".to_string(),","highlight_start":1,"highlight_end":39},{"text":"        extra,","highlight_start":1,"highlight_end":15},{"text":"    )","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"(\"haier-usdk\".to_string(), device_id.to_string(), Some(\"fake_type_id1\".to_string()), Some(\"fake_type_code1\".to_string()), \"fake_type_name1\".to_string(), \"fake_model1\".to_string(), Some(\"fake_parent_id1\".to_string()), \"fake_pro_no1\".to_string(), /* UpDeviceBasic */, /* UpDevicePermission */, /* UpDeviceProduct */, /* UpDeviceRelation */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 12 arguments but 10 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:123:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    UpDeviceInfo::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m_____\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"haier-usdk\".to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        device_id.to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"fake_type_id1\".to_string()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"fake_type_name1\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `Option<std::string::String>`, found `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m128\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"fake_type_code1\".to_string()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `std::string::String`, found `Option<std::string::String>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"fake_model1\".to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"fake_pro_no1\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `Option<std::string::String>`, found `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"fake_parent_id1\".to_string()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `std::string::String`, found `Option<std::string::String>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"fake_device_id2\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `UpDeviceBasic`, found `String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m133\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        extra,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `UpDevicePermission`, found `String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mtwo arguments of type `UpDeviceProduct` and `UpDeviceRelation` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/repo_Uplus/updevice_rust/rust_updevice/src/models/device_info.rs:27:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: did you mean\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    UpDeviceInfo::new\u001b[0m\u001b[0m\u001b[38;5;10m(\"haier-usdk\".to_string(), device_id.to_string(), Some(\"fake_type_id1\".to_string()), Some(\"fake_type_code1\".to_string()), \"fake_type_name1\".to_string(), \"fake_model1\".to_string(), Some(\"fake_parent_id1\".to_string()), \"fake_pro_no1\".to_string(), /* UpDeviceBasic */, /* UpDevicePermission */, /* UpDeviceProduct */, /* UpDeviceRelation */)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6786,"byte_end":6790,"line_start":157,"line_end":157,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"            None","highlight_start":13,"highlight_end":17}],"label":"expected `String`, found `Option<_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected struct `std::string::String`\n     found enum `Option<_>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:157:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m157\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            None\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `String`, found `Option<_>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected struct `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::string::String\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m                 found enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mOption<_>\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6820,"byte_end":6851,"line_start":159,"line_end":159,"column_start":13,"column_end":44,"is_primary":true,"text":[{"text":"            Some(parent_id_str.to_string())","highlight_start":13,"highlight_end":44}],"label":"expected `String`, found `Option<String>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected struct `std::string::String`\n     found enum `Option<std::string::String>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:159:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(parent_id_str.to_string())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `String`, found `Option<String>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected struct `std::string::String`\u001b[0m\n\u001b[0m                 found enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mOption<\u001b[0m\u001b[0mstd::string::String\u001b[0m\u001b[0m\u001b[1m\u001b[35m>\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 12 arguments but 10 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6871,"byte_end":6900,"line_start":161,"line_end":161,"column_start":9,"column_end":38,"is_primary":false,"text":[{"text":"        \"fake_device_id2\".to_string(),","highlight_start":9,"highlight_end":38}],"label":"expected `UpDeviceBasic`, found `String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6910,"byte_end":6915,"line_start":162,"line_end":162,"column_start":9,"column_end":14,"is_primary":false,"text":[{"text":"        extra,","highlight_start":9,"highlight_end":14}],"label":"expected `UpDevicePermission`, found `String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6471,"byte_end":6922,"line_start":148,"line_end":163,"column_start":40,"column_end":6,"is_primary":false,"text":[{"text":"    let device_info = UpDeviceInfo::new(","highlight_start":40,"highlight_end":41},{"text":"        \"haier-usdk\".to_string(),","highlight_start":1,"highlight_end":34},{"text":"        device_id.to_string(),","highlight_start":1,"highlight_end":31},{"text":"        Some(\"fake_type_id1\".to_string()),","highlight_start":1,"highlight_end":43},{"text":"        \"fake_type_name1\".to_string(),","highlight_start":1,"highlight_end":39},{"text":"        Some(\"fake_type_code1\".to_string()),","highlight_start":1,"highlight_end":45},{"text":"        \"fake_model1\".to_string(),","highlight_start":1,"highlight_end":35},{"text":"        \"fake_pro_no1\".to_string(),","highlight_start":1,"highlight_end":36},{"text":"        if parent_id_str.is_empty() {","highlight_start":1,"highlight_end":38},{"text":"            None","highlight_start":1,"highlight_end":17},{"text":"        } else {","highlight_start":1,"highlight_end":17},{"text":"            Some(parent_id_str.to_string())","highlight_start":1,"highlight_end":44},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        \"fake_device_id2\".to_string(),","highlight_start":1,"highlight_end":39},{"text":"        extra,","highlight_start":1,"highlight_end":15},{"text":"    );","highlight_start":1,"highlight_end":6}],"label":"two arguments of type `UpDeviceProduct` and `UpDeviceRelation` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6589,"byte_end":6618,"line_start":152,"line_end":152,"column_start":9,"column_end":38,"is_primary":false,"text":[{"text":"        \"fake_type_name1\".to_string(),","highlight_start":9,"highlight_end":38}],"label":"expected `Option<std::string::String>`, found `std::string::String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6628,"byte_end":6663,"line_start":153,"line_end":153,"column_start":9,"column_end":44,"is_primary":false,"text":[{"text":"        Some(\"fake_type_code1\".to_string()),","highlight_start":9,"highlight_end":44}],"label":"expected `std::string::String`, found `Option<std::string::String>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6454,"byte_end":6471,"line_start":148,"line_end":148,"column_start":23,"column_end":40,"is_primary":true,"text":[{"text":"    let device_info = UpDeviceInfo::new(","highlight_start":23,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected `Option<String>`, found `String`","code":null,"level":"note","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6708,"byte_end":6734,"line_start":155,"line_end":155,"column_start":9,"column_end":35,"is_primary":true,"text":[{"text":"        \"fake_pro_no1\".to_string(),","highlight_start":9,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"expected enum `Option<std::string::String>`\n found struct `std::string::String`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/repo_Uplus/updevice_rust/rust_updevice/src/models/device_info.rs","byte_start":922,"byte_end":925,"line_start":27,"line_end":27,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"try wrapping the expression in `Some`","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6708,"byte_end":6708,"line_start":155,"line_end":155,"column_start":9,"column_end":9,"is_primary":true,"text":[{"text":"        \"fake_pro_no1\".to_string(),","highlight_start":9,"highlight_end":9}],"label":null,"suggested_replacement":"Some(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6734,"byte_end":6734,"line_start":155,"line_end":155,"column_start":35,"column_end":35,"is_primary":true,"text":[{"text":"        \"fake_pro_no1\".to_string(),","highlight_start":35,"highlight_end":35}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"did you mean","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":6471,"byte_end":6922,"line_start":148,"line_end":163,"column_start":40,"column_end":6,"is_primary":true,"text":[{"text":"    let device_info = UpDeviceInfo::new(","highlight_start":40,"highlight_end":41},{"text":"        \"haier-usdk\".to_string(),","highlight_start":1,"highlight_end":34},{"text":"        device_id.to_string(),","highlight_start":1,"highlight_end":31},{"text":"        Some(\"fake_type_id1\".to_string()),","highlight_start":1,"highlight_end":43},{"text":"        \"fake_type_name1\".to_string(),","highlight_start":1,"highlight_end":39},{"text":"        Some(\"fake_type_code1\".to_string()),","highlight_start":1,"highlight_end":45},{"text":"        \"fake_model1\".to_string(),","highlight_start":1,"highlight_end":35},{"text":"        \"fake_pro_no1\".to_string(),","highlight_start":1,"highlight_end":36},{"text":"        if parent_id_str.is_empty() {","highlight_start":1,"highlight_end":38},{"text":"            None","highlight_start":1,"highlight_end":17},{"text":"        } else {","highlight_start":1,"highlight_end":17},{"text":"            Some(parent_id_str.to_string())","highlight_start":1,"highlight_end":44},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        \"fake_device_id2\".to_string(),","highlight_start":1,"highlight_end":39},{"text":"        extra,","highlight_start":1,"highlight_end":15},{"text":"    );","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"(\"haier-usdk\".to_string(), device_id.to_string(), Some(\"fake_type_id1\".to_string()), Some(\"fake_type_code1\".to_string()), \"fake_type_name1\".to_string(), \"fake_model1\".to_string(), /* Option<std::string::String> */, if parent_id_str.is_empty() {\n            None\n        } else {\n            Some(parent_id_str.to_string())\n        }, /* UpDeviceBasic */, /* UpDevicePermission */, /* UpDeviceProduct */, /* UpDeviceRelation */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 12 arguments but 10 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:148:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let device_info = UpDeviceInfo::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m_______________________\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"haier-usdk\".to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        device_id.to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"fake_type_id1\".to_string()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m152\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"fake_type_name1\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `Option<std::string::String>`, found `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"fake_type_code1\".to_string()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `std::string::String`, found `Option<std::string::String>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"fake_device_id2\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `UpDeviceBasic`, found `String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        extra,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `UpDevicePermission`, found `String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    );\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mtwo arguments of type `UpDeviceProduct` and `UpDeviceRelation` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: expected `Option<String>`, found `String`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:155:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"fake_pro_no1\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mOption<\u001b[0m\u001b[0mstd::string::String\u001b[0m\u001b[0m\u001b[1m\u001b[35m>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m             found struct `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/repo_Uplus/updevice_rust/rust_updevice/src/models/device_info.rs:27:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: try wrapping the expression in `Some`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[38;5;10mSome(\u001b[0m\u001b[0m\"fake_pro_no1\".to_string()\u001b[0m\u001b[0m\u001b[38;5;10m)\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: did you mean\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    let device_info = UpDeviceInfo::new\u001b[0m\u001b[0m\u001b[38;5;10m(\"haier-usdk\".to_string(), device_id.to_string(), Some(\"fake_type_id1\".to_string()), Some(\"fake_type_code1\".to_string()), \"fake_type_name1\".to_string(), \"fake_model1\".to_string(), /* Option<std::string::String> */, if parent_id_str.is_empty() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             None\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+         } else {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             Some(parent_id_str.to_string())\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m152\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~         }, /* UpDeviceBasic */, /* UpDevicePermission */, /* UpDeviceProduct */, /* UpDeviceRelation */)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no variant or associated item named `NetworkError` found for enum `DeviceError` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":11412,"byte_end":11424,"line_start":269,"line_end":269,"column_start":30,"column_end":42,"is_primary":true,"text":[{"text":"    let error = DeviceError::NetworkError(error_message);","highlight_start":30,"highlight_end":42}],"label":"variant or associated item not found in `DeviceError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no variant or associated item named `NetworkError` found for enum `DeviceError` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:269:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m269\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let error = DeviceError::NetworkError(error_message);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvariant or associated item not found in `DeviceError`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"attempted to take value of method `device_id` on type `&UpDeviceInfo`","code":{"code":"E0615","explanation":"Attempted to access a method like a field.\n\nErroneous code example:\n\n```compile_fail,E0615\nstruct Foo {\n    x: u32,\n}\n\nimpl Foo {\n    fn method(&self) {}\n}\n\nlet f = Foo { x: 0 };\nf.method; // error: attempted to take value of method `method` on type `Foo`\n```\n\nIf you want to use a method, add `()` after it:\n\n```\n# struct Foo { x: u32 }\n# impl Foo { fn method(&self) {} }\n# let f = Foo { x: 0 };\nf.method();\n```\n\nHowever, if you wanted to access a field of a struct check that the field name\nis spelled correctly. Example:\n\n```\n# struct Foo { x: u32 }\n# impl Foo { fn method(&self) {} }\n# let f = Foo { x: 0 };\nprintln!(\"{}\", f.x);\n```\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":12989,"byte_end":12998,"line_start":299,"line_end":299,"column_start":34,"column_end":43,"is_primary":true,"text":[{"text":"            .any(|device| device.device_id == expected_id);","highlight_start":34,"highlight_end":43}],"label":"method, not a field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"use parentheses to call the method","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":12998,"byte_end":12998,"line_start":299,"line_end":299,"column_start":43,"column_end":43,"is_primary":true,"text":[{"text":"            .any(|device| device.device_id == expected_id);","highlight_start":43,"highlight_end":43}],"label":null,"suggested_replacement":"()","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0615]\u001b[0m\u001b[0m\u001b[1m: attempted to take value of method `device_id` on type `&UpDeviceInfo`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:299:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .any(|device| device.device_id == expected_id);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod, not a field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use parentheses to call the method\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            .any(|device| device.device_id\u001b[0m\u001b[0m\u001b[38;5;10m()\u001b[0m\u001b[0m == expected_id);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[38;5;10m++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"attempted to take value of method `device_id` on type `&UpDeviceInfo`","code":{"code":"E0615","explanation":"Attempted to access a method like a field.\n\nErroneous code example:\n\n```compile_fail,E0615\nstruct Foo {\n    x: u32,\n}\n\nimpl Foo {\n    fn method(&self) {}\n}\n\nlet f = Foo { x: 0 };\nf.method; // error: attempted to take value of method `method` on type `Foo`\n```\n\nIf you want to use a method, add `()` after it:\n\n```\n# struct Foo { x: u32 }\n# impl Foo { fn method(&self) {} }\n# let f = Foo { x: 0 };\nf.method();\n```\n\nHowever, if you wanted to access a field of a struct check that the field name\nis spelled correctly. Example:\n\n```\n# struct Foo { x: u32 }\n# impl Foo { fn method(&self) {} }\n# let f = Foo { x: 0 };\nprintln!(\"{}\", f.x);\n```\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":15987,"byte_end":15996,"line_start":359,"line_end":359,"column_start":55,"column_end":64,"is_primary":true,"text":[{"text":"        let found = result.iter().any(|device| device.device_id == expected_id);","highlight_start":55,"highlight_end":64}],"label":"method, not a field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"use parentheses to call the method","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":15996,"byte_end":15996,"line_start":359,"line_end":359,"column_start":64,"column_end":64,"is_primary":true,"text":[{"text":"        let found = result.iter().any(|device| device.device_id == expected_id);","highlight_start":64,"highlight_end":64}],"label":null,"suggested_replacement":"()","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0615]\u001b[0m\u001b[0m\u001b[1m: attempted to take value of method `device_id` on type `&UpDeviceInfo`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:359:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let found = result.iter().any(|device| device.device_id == expected_id);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod, not a field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use parentheses to call the method\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        let found = result.iter().any(|device| device.device_id\u001b[0m\u001b[0m\u001b[38;5;10m()\u001b[0m\u001b[0m == expected_id);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[38;5;10m++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a mutable reference to mutable static is discouraged","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":5580,"byte_end":5593,"line_start":123,"line_end":123,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":18,"highlight_end":31}],"label":"mutable reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see issue #114447 <https://github.com/rust-lang/rust/issues/114447>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"this will be a hard error in the 2024 edition","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"this mutable reference has lifetime `'static`, but if the static gets accessed (read or written) by any other means, or any other reference is created, then any further use of this mutable reference is Undefined Behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(static_mut_refs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"use `addr_of_mut!` instead to create a raw pointer","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":5580,"byte_end":5585,"line_start":123,"line_end":123,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":"addr_of_mut!(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":5593,"byte_end":5593,"line_start":123,"line_end":123,"column_start":31,"column_end":31,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":31,"highlight_end":31}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a mutable reference to mutable static is discouraged\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/utils/device_test_holder.rs:123:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        unsafe { &mut INSTANCE }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mmutable reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see issue #114447 <https://github.com/rust-lang/rust/issues/114447>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this will be a hard error in the 2024 edition\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this mutable reference has lifetime `'static`, but if the static gets accessed (read or written) by any other means, or any other reference is created, then any further use of this mutable reference is Undefined Behavior\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(static_mut_refs)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `addr_of_mut!` instead to create a raw pointer\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        unsafe { \u001b[0m\u001b[0m\u001b[38;5;10maddr_of_mut!(\u001b[0m\u001b[0mINSTANCE\u001b[0m\u001b[0m\u001b[38;5;10m)\u001b[0m\u001b[0m }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~~~~~~~~\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":23580,"byte_end":23594,"line_start":610,"line_end":610,"column_start":57,"column_end":71,"is_primary":true,"text":[{"text":"            Some(DataHolder::DeviceInfoList(result)) => result.clone(),","highlight_start":57,"highlight_end":71}],"label":"expected `Result<Vec<...>, ...>`, found `&Result<Vec<...>, ...>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":23445,"byte_end":23470,"line_start":608,"line_end":608,"column_start":45,"column_end":70,"is_primary":false,"text":[{"text":"    pub fn get_device_list_result(&self) -> Result<Vec<UpDeviceInfo>> {","highlight_start":45,"highlight_end":70}],"label":"expected `Result<Vec<UpDeviceInfo>, DeviceError>` because of return type","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"  expected enum `Result<_, _>`\nfound reference `&Result<_, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`Result<Vec<UpDeviceInfo>, DeviceError>` does not implement `Clone`, so `&Result<Vec<UpDeviceInfo>, DeviceError>` was cloned instead","code":null,"level":"note","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":23580,"byte_end":23586,"line_start":610,"line_end":610,"column_start":57,"column_end":63,"is_primary":true,"text":[{"text":"            Some(DataHolder::DeviceInfoList(result)) => result.clone(),","highlight_start":57,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`Clone` is not implemented because the trait bound `DeviceError: Clone` is not satisfied","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/utils/device_test_holder.rs:610:57\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m608\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_device_list_result(&self) -> Result<Vec<UpDeviceInfo>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `Result<Vec<UpDeviceInfo>, DeviceError>` because of return type\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m609\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        match self.result_map.get(\"device_list\") {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m610\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(DataHolder::DeviceInfoList(result)) => result.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Result<Vec<...>, ...>`, found `&Result<Vec<...>, ...>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m:   expected enum `Result<_, _>`\u001b[0m\n\u001b[0m            found reference `\u001b[0m\u001b[0m\u001b[1m\u001b[35m&\u001b[0m\u001b[0mResult<_, _>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Result<Vec<UpDeviceInfo>, DeviceError>` does not implement `Clone`, so `&Result<Vec<UpDeviceInfo>, DeviceError>` was cloned instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/utils/device_test_holder.rs:610:57\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m610\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(DataHolder::DeviceInfoList(result)) => result.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: `Clone` is not implemented because the trait bound `DeviceError: Clone` is not satisfied\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no variant or associated item named `NetworkError` found for enum `DeviceError` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":23657,"byte_end":23669,"line_start":611,"line_end":611,"column_start":62,"column_end":74,"is_primary":true,"text":[{"text":"            _ => Err(rust_updevice::api::error::DeviceError::NetworkError(","highlight_start":62,"highlight_end":74}],"label":"variant or associated item not found in `DeviceError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no variant or associated item named `NetworkError` found for enum `DeviceError` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/utils/device_test_holder.rs:611:62\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m611\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            _ => Err(rust_updevice::api::error::DeviceError::NetworkError(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvariant or associated item not found in `DeviceError`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":25225,"byte_end":25239,"line_start":654,"line_end":654,"column_start":48,"column_end":62,"is_primary":true,"text":[{"text":"            Some(DataHolder::Empty(result)) => result.clone(),","highlight_start":48,"highlight_end":62}],"label":"expected `Result<(), DeviceError>`, found `&Result<(), DeviceError>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":25113,"byte_end":25123,"line_start":652,"line_end":652,"column_start":46,"column_end":56,"is_primary":false,"text":[{"text":"    pub fn get_update_cache_result(&self) -> Result<()> {","highlight_start":46,"highlight_end":56}],"label":"expected `Result<(), DeviceError>` because of return type","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"  expected enum `Result<_, _>`\nfound reference `&Result<_, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`Result<(), DeviceError>` does not implement `Clone`, so `&Result<(), DeviceError>` was cloned instead","code":null,"level":"note","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":25225,"byte_end":25231,"line_start":654,"line_end":654,"column_start":48,"column_end":54,"is_primary":true,"text":[{"text":"            Some(DataHolder::Empty(result)) => result.clone(),","highlight_start":48,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`Clone` is not implemented because the trait bound `DeviceError: Clone` is not satisfied","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/utils/device_test_holder.rs:654:48\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m652\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_update_cache_result(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `Result<(), DeviceError>` because of return type\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m653\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        match self.result_map.get(\"update_cache\") {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m654\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(DataHolder::Empty(result)) => result.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Result<(), DeviceError>`, found `&Result<(), DeviceError>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m:   expected enum `Result<_, _>`\u001b[0m\n\u001b[0m            found reference `\u001b[0m\u001b[0m\u001b[1m\u001b[35m&\u001b[0m\u001b[0mResult<_, _>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Result<(), DeviceError>` does not implement `Clone`, so `&Result<(), DeviceError>` was cloned instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/utils/device_test_holder.rs:654:48\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m654\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(DataHolder::Empty(result)) => result.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: `Clone` is not implemented because the trait bound `DeviceError: Clone` is not satisfied\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::device::up_device::UpDevice`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":780,"byte_end":822,"line_start":19,"line_end":19,"column_start":5,"column_end":47,"is_primary":true,"text":[{"text":"use rust_updevice::device::up_device::UpDevice;","highlight_start":5,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::device::up_device::UpDevice`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::device::up_device::UpDevice;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `subscription_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs","byte_start":1526,"byte_end":1541,"line_start":48,"line_end":48,"column_start":46,"column_end":61,"is_primary":true,"text":[{"text":"    fn unsubscribe_device_list_change(&self, subscription_id: &str) {","highlight_start":46,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs","byte_start":1526,"byte_end":1541,"line_start":48,"line_end":48,"column_start":46,"column_end":61,"is_primary":true,"text":[{"text":"    fn unsubscribe_device_list_change(&self, subscription_id: &str) {","highlight_start":46,"highlight_end":61}],"label":null,"suggested_replacement":"_subscription_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `subscription_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs:48:46\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn unsubscribe_device_list_change(&self, subscription_id: &str) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_subscription_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `area`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1138,"byte_end":1142,"line_start":34,"line_end":34,"column_start":25,"column_end":29,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":25,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1138,"byte_end":1142,"line_start":34,"line_end":34,"column_start":25,"column_end":29,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":25,"highlight_end":29}],"label":null,"suggested_replacement":"_area","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `area`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:34:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_area`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_info`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1150,"byte_end":1158,"line_start":34,"line_end":34,"column_start":37,"column_end":45,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":37,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1150,"byte_end":1158,"line_start":34,"line_end":34,"column_start":37,"column_end":45,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":37,"highlight_end":45}],"label":null,"suggested_replacement":"_app_info","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `app_info`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:34:37\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mrea: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_app_info`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `client_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1169,"byte_end":1178,"line_start":34,"line_end":34,"column_start":56,"column_end":65,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":56,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1169,"byte_end":1178,"line_start":34,"line_end":34,"column_start":56,"column_end":65,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":56,"highlight_end":65}],"label":null,"suggested_replacement":"_client_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `client_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:34:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_client_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `enable_http_nds`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1188,"byte_end":1203,"line_start":34,"line_end":34,"column_start":75,"column_end":90,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":75,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1188,"byte_end":1203,"line_start":34,"line_end":34,"column_start":75,"column_end":90,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":75,"highlight_end":90}],"label":null,"suggested_replacement":"_enable_http_nds","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `enable_http_nds`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:34:75\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mng, enable_http_nds: bool) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_enable_http_nds`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1287,"byte_end":1294,"line_start":38,"line_end":38,"column_start":38,"column_end":45,"is_primary":true,"text":[{"text":"    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {","highlight_start":38,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1287,"byte_end":1294,"line_start":38,"line_end":38,"column_start":38,"column_end":45,"is_primary":true,"text":[{"text":"    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {","highlight_start":38,"highlight_end":45}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:38:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `access_token`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1304,"byte_end":1316,"line_start":38,"line_end":38,"column_start":55,"column_end":67,"is_primary":true,"text":[{"text":"    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {","highlight_start":55,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1304,"byte_end":1316,"line_start":38,"line_end":38,"column_start":55,"column_end":67,"is_primary":true,"text":[{"text":"    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {","highlight_start":55,"highlight_end":67}],"label":null,"suggested_replacement":"_access_token","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `access_token`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:38:55\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_access_token`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `access_token`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1478,"byte_end":1490,"line_start":46,"line_end":46,"column_start":35,"column_end":47,"is_primary":true,"text":[{"text":"    fn update_access_token(&self, access_token: String) -> Result<()> {","highlight_start":35,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1478,"byte_end":1490,"line_start":46,"line_end":46,"column_start":35,"column_end":47,"is_primary":true,"text":[{"text":"    fn update_access_token(&self, access_token: String) -> Result<()> {","highlight_start":35,"highlight_end":47}],"label":null,"suggested_replacement":"_access_token","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `access_token`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:46:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m46\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn update_access_token(&self, access_token: String) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_access_token`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `listener`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1582,"byte_end":1590,"line_start":50,"line_end":50,"column_start":44,"column_end":52,"is_primary":true,"text":[{"text":"    fn subscribe_device_list_change(&self, listener: FnClone<ToolkitEvent>) -> Result<String> {","highlight_start":44,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1582,"byte_end":1590,"line_start":50,"line_end":50,"column_start":44,"column_end":52,"is_primary":true,"text":[{"text":"    fn subscribe_device_list_change(&self, listener: FnClone<ToolkitEvent>) -> Result<String> {","highlight_start":44,"highlight_end":52}],"label":null,"suggested_replacement":"_listener","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `listener`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:50:44\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn subscribe_device_list_change(&self, listener: FnClone<ToolkitEvent>) -> Result<String> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_listener`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `subscription_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2175,"byte_end":2190,"line_start":69,"line_end":69,"column_start":41,"column_end":56,"is_primary":true,"text":[{"text":"    fn unsubscribe_device_change(&self, subscription_id: String) -> Result<()> {","highlight_start":41,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2175,"byte_end":2190,"line_start":69,"line_end":69,"column_start":41,"column_end":56,"is_primary":true,"text":[{"text":"    fn unsubscribe_device_change(&self, subscription_id: String) -> Result<()> {","highlight_start":41,"highlight_end":56}],"label":null,"suggested_replacement":"_subscription_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `subscription_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:69:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m69\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn unsubscribe_device_change(&self, subscription_id: String) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_subscription_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2292,"byte_end":2301,"line_start":75,"line_end":75,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        device_id: &str,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2292,"byte_end":2301,"line_start":75,"line_end":75,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        device_id: &str,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:75:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        device_id: &str,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `command`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2317,"byte_end":2324,"line_start":76,"line_end":76,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        command: UpDeviceCommand,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2317,"byte_end":2324,"line_start":76,"line_end":76,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        command: UpDeviceCommand,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_command","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `command`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:76:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        command: UpDeviceCommand,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_command`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `timeout`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2351,"byte_end":2358,"line_start":77,"line_end":77,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        timeout: uhsd_u32,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2351,"byte_end":2358,"line_start":77,"line_end":77,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        timeout: uhsd_u32,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_timeout","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `timeout`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:77:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        timeout: uhsd_u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_timeout`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `route`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2378,"byte_end":2383,"line_start":78,"line_end":78,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"        route: DeviceCtrlRoute,","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2378,"byte_end":2383,"line_start":78,"line_end":78,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"        route: DeviceCtrlRoute,","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":"_route","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `route`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:78:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        route: DeviceCtrlRoute,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_route`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2583,"byte_end":2592,"line_start":87,"line_end":87,"column_start":41,"column_end":50,"is_primary":true,"text":[{"text":"    fn get_device_attribute_list(&self, device_id: &str) -> Result<Vec<UpDeviceAttribute>> {","highlight_start":41,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2583,"byte_end":2592,"line_start":87,"line_end":87,"column_start":41,"column_end":50,"is_primary":true,"text":[{"text":"    fn get_device_attribute_list(&self, device_id: &str) -> Result<Vec<UpDeviceAttribute>> {","highlight_start":41,"highlight_end":50}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:87:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m87\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_device_attribute_list(&self, device_id: &str) -> Result<Vec<UpDeviceAttribute>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2689,"byte_end":2698,"line_start":91,"line_end":91,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"    fn get_device_info(&self, device_id: &str) -> Result<DeviceInfo> {","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2689,"byte_end":2698,"line_start":91,"line_end":91,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"    fn get_device_info(&self, device_id: &str) -> Result<DeviceInfo> {","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:91:31\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m91\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_device_info(&self, device_id: &str) -> Result<DeviceInfo> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_config_data_source.rs","byte_start":515,"byte_end":524,"line_start":17,"line_end":17,"column_start":39,"column_end":48,"is_primary":true,"text":[{"text":"    async fn get_device_config(&self, device_id: &str) -> Result<String, ConfigDataSourceError> {","highlight_start":39,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_config_data_source.rs","byte_start":515,"byte_end":524,"line_start":17,"line_end":17,"column_start":39,"column_end":48,"is_primary":true,"text":[{"text":"    async fn get_device_config(&self, device_id: &str) -> Result<String, ConfigDataSourceError> {","highlight_start":39,"highlight_end":48}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_config_data_source.rs:17:39\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_device_config(&self, device_id: &str) -> Result<String, ConfigDataSourceError> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":16649,"byte_end":16655,"line_start":378,"line_end":378,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let result = identification_string_to_bool(result_str);","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":16649,"byte_end":16655,"line_start":378,"line_end":378,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let result = identification_string_to_bool(result_str);","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `result`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:378:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m378\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let result = identification_string_to_bool(result_str);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_result`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `mock_toolkit`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":16709,"byte_end":16721,"line_start":379,"line_end":379,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let mock_toolkit = get_mock_device_toolkit_mut();","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":16709,"byte_end":16721,"line_start":379,"line_end":379,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let mock_toolkit = get_mock_device_toolkit_mut();","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_mock_toolkit","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `mock_toolkit`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:379:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m379\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mock_toolkit = get_mock_device_toolkit_mut();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_mock_toolkit`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":30982,"byte_end":31000,"line_start":744,"line_end":744,"column_start":21,"column_end":39,"is_primary":true,"text":[{"text":"        if let Some(mut filters_holder) =","highlight_start":21,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":30982,"byte_end":30986,"line_start":744,"line_end":744,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"        if let Some(mut filters_holder) =","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:744:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m744\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(mut filters_holder) =\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `protocol`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":32638,"byte_end":32646,"line_start":782,"line_end":782,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    protocol: String,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":32638,"byte_end":32646,"line_start":782,"line_end":782,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    protocol: String,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":"_protocol","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `protocol`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:782:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m782\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    protocol: String,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_protocol`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"borrow of moved value: `subscriber`","code":{"code":"E0382","explanation":"A variable was used after its contents have been moved elsewhere.\n\nErroneous code example:\n\n```compile_fail,E0382\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = MyStruct{ s: 5u32 };\n    let y = x;\n    x.s = 6;\n    println!(\"{}\", x.s);\n}\n```\n\nSince `MyStruct` is a type that is not marked `Copy`, the data gets moved out\nof `x` when we set `y`. This is fundamental to Rust's ownership system: outside\nof workarounds like `Rc`, a value cannot be owned by more than one variable.\n\nSometimes we don't need to move the value. Using a reference, we can let another\nfunction borrow the value without changing its ownership. In the example below,\nwe don't actually have to move our string to `calculate_length`, we can give it\na reference to it with `&` instead.\n\n```\nfn main() {\n    let s1 = String::from(\"hello\");\n\n    let len = calculate_length(&s1);\n\n    println!(\"The length of '{}' is {}.\", s1, len);\n}\n\nfn calculate_length(s: &String) -> usize {\n    s.len()\n}\n```\n\nA mutable reference can be created with `&mut`.\n\nSometimes we don't want a reference, but a duplicate. All types marked `Clone`\ncan be duplicated by calling `.clone()`. Subsequent changes to a clone do not\naffect the original variable.\n\nMost types in the standard library are marked `Clone`. The example below\ndemonstrates using `clone()` on a string. `s1` is first set to \"many\", and then\ncopied to `s2`. Then the first character of `s1` is removed, without affecting\n`s2`. \"any many\" is printed to the console.\n\n```\nfn main() {\n    let mut s1 = String::from(\"many\");\n    let s2 = s1.clone();\n    s1.remove(0);\n    println!(\"{} {}\", s1, s2);\n}\n```\n\nIf we control the definition of a type, we can implement `Clone` on it ourselves\nwith `#[derive(Clone)]`.\n\nSome types have no ownership semantics at all and are trivial to duplicate. An\nexample is `i32` and the other number types. We don't have to call `.clone()` to\nclone them, because they are marked `Copy` in addition to `Clone`. Implicit\ncloning is more convenient in this case. We can mark our own types `Copy` if\nall their members also are marked `Copy`.\n\nIn the example below, we implement a `Point` type. Because it only stores two\nintegers, we opt-out of ownership semantics with `Copy`. Then we can\n`let p2 = p1` without `p1` being moved.\n\n```\n#[derive(Copy, Clone)]\nstruct Point { x: i32, y: i32 }\n\nfn main() {\n    let mut p1 = Point{ x: -1, y: 2 };\n    let p2 = p1;\n    p1.x = 1;\n    println!(\"p1: {}, {}\", p1.x, p1.y);\n    println!(\"p2: {}, {}\", p2.x, p2.y);\n}\n```\n\nAlternatively, if we don't control the struct's definition, or mutable shared\nownership is truly required, we can use `Rc` and `RefCell`:\n\n```\nuse std::cell::RefCell;\nuse std::rc::Rc;\n\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = Rc::new(RefCell::new(MyStruct{ s: 5u32 }));\n    let y = x.clone();\n    x.borrow_mut().s = 6;\n    println!(\"{}\", x.borrow().s);\n}\n```\n\nWith this approach, x and y share ownership of the data via the `Rc` (reference\ncount type). `RefCell` essentially performs runtime borrow checking: ensuring\nthat at most one writer or multiple readers can access the data at any one time.\n\nIf you wish to learn more about ownership in Rust, start with the\n[Understanding Ownership][understanding-ownership] chapter in the Book.\n\n[understanding-ownership]: https://doc.rust-lang.org/book/ch04-00-understanding-ownership.html\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":7642,"byte_end":7654,"line_start":179,"line_end":179,"column_start":84,"column_end":96,"is_primary":false,"text":[{"text":"    let subscription_id = user_data_source.subscribe_user_info_change(FnClone::new(move |event| {","highlight_start":84,"highlight_end":96}],"label":"value moved into closure here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":7970,"byte_end":7980,"line_start":186,"line_end":186,"column_start":46,"column_end":56,"is_primary":false,"text":[{"text":"            .capture_user_event_for_listener(subscriber.clone(), event_str.to_string());","highlight_start":46,"highlight_end":56}],"label":"variable moved due to use in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":8090,"byte_end":8101,"line_start":188,"line_end":188,"column_start":68,"column_end":79,"is_primary":true,"text":[{"text":"    UpDeviceTestHolder::get_instance().record_user_subscription_id(&subscriber, &subscription_id);","highlight_start":68,"highlight_end":79}],"label":"value borrowed here after move","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_models_steps.rs","byte_start":7452,"byte_end":7462,"line_start":177,"line_end":177,"column_start":58,"column_end":68,"is_primary":false,"text":[{"text":"fn when_subscribe_user_info_change(_world: &mut MyWorld, subscriber: String) {","highlight_start":58,"highlight_end":68}],"label":"move occurs because `subscriber` has type `std::string::String`, which does not implement the `Copy` trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0382]\u001b[0m\u001b[0m\u001b[1m: borrow of moved value: `subscriber`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_models_steps.rs:188:68\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn when_subscribe_user_info_change(_world: &mut MyWorld, subscriber: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmove occurs because `subscriber` has type `std::string::String`, which does not implement the `Copy` trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let user_data_source = UpDeviceInjection::get_instance().get_user_data_source();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m179\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let subscription_id = user_data_source.subscribe_user_info_change(FnClone::new(move |event| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mvalue moved into closure here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m186\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .capture_user_event_for_listener(subscriber.clone(), event_str.to_string());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mvariable moved due to use in closure\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    UpDeviceTestHolder::get_instance().record_user_subscription_id(&subscriber, &subscription_id);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvalue borrowed here after move\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `connect_state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":28855,"byte_end":28868,"line_start":711,"line_end":711,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let connect_state = if is_ready {","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":28855,"byte_end":28868,"line_start":711,"line_end":711,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let connect_state = if is_ready {","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_connect_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `connect_state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/engine_device_steps.rs:711:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m711\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let connect_state = if is_ready {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_connect_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `mock_engine_device`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":28992,"byte_end":29010,"line_start":716,"line_end":716,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    let mock_engine_device = UpDeviceTestHolder::get_instance().get_engine_device();","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":28992,"byte_end":29010,"line_start":716,"line_end":716,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    let mock_engine_device = UpDeviceTestHolder::get_instance().get_engine_device();","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":"_mock_engine_device","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `mock_engine_device`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/engine_device_steps.rs:716:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m716\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mock_engine_device = UpDeviceTestHolder::get_instance().get_engine_device();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_mock_engine_device`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":3896,"byte_end":3905,"line_start":82,"line_end":82,"column_start":52,"column_end":61,"is_primary":true,"text":[{"text":"fn when_device_status_change(_world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":52,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":3896,"byte_end":3905,"line_start":82,"line_end":82,"column_start":52,"column_end":61,"is_primary":true,"text":[{"text":"fn when_device_status_change(_world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":52,"highlight_end":61}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:82:52\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn when_device_status_change(_world: &mut MyWorld, device_id: String, step: &Step) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":5362,"byte_end":5367,"line_start":118,"line_end":118,"column_start":42,"column_end":47,"is_primary":true,"text":[{"text":"fn then_user_query_device_attribute_list(world: &mut MyWorld, _device_id: String, step: &Step) {","highlight_start":42,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":5362,"byte_end":5367,"line_start":118,"line_end":118,"column_start":42,"column_end":47,"is_primary":true,"text":[{"text":"fn then_user_query_device_attribute_list(world: &mut MyWorld, _device_id: String, step: &Step) {","highlight_start":42,"highlight_end":47}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:118:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_user_query_device_attribute_list(world: &mut MyWorld, _device_id: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":6059,"byte_end":6064,"line_start":133,"line_end":133,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"fn then_user_query_real_device_attribute_list(world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":6059,"byte_end":6064,"line_start":133,"line_end":133,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"fn then_user_query_real_device_attribute_list(world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:133:47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m133\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_user_query_real_device_attribute_list(world: &mut MyWorld, device_id: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":7700,"byte_end":7709,"line_start":170,"line_end":170,"column_start":60,"column_end":69,"is_primary":true,"text":[{"text":"fn then_user_query_device_alarm_list(_world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":60,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":7700,"byte_end":7709,"line_start":170,"line_end":170,"column_start":60,"column_end":69,"is_primary":true,"text":[{"text":"fn then_user_query_device_alarm_list(_world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":60,"highlight_end":69}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:170:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m170\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_user_query_device_alarm_list(_world: &mut MyWorld, device_id: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":9101,"byte_end":9110,"line_start":204,"line_end":204,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    device_id: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":9101,"byte_end":9110,"line_start":204,"line_end":204,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    device_id: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:204:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    device_id: String,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":10180,"byte_end":10189,"line_start":236,"line_end":236,"column_start":62,"column_end":71,"is_primary":true,"text":[{"text":"fn then_user_query_device_online_state(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":62,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":10180,"byte_end":10189,"line_start":236,"line_end":236,"column_start":62,"column_end":71,"is_primary":true,"text":[{"text":"fn then_user_query_device_online_state(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":62,"highlight_end":71}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:236:62\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_user_query_device_online_state(_world: &mut MyWorld, device_id: String, state_str: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":11174,"byte_end":11183,"line_start":264,"line_end":264,"column_start":62,"column_end":71,"is_primary":true,"text":[{"text":"fn when_device_connection_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":62,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":11174,"byte_end":11183,"line_start":264,"line_end":264,"column_start":62,"column_end":71,"is_primary":true,"text":[{"text":"fn when_device_connection_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":62,"highlight_end":71}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:264:62\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m264\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn when_device_connection_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":12261,"byte_end":12270,"line_start":294,"line_end":294,"column_start":58,"column_end":67,"is_primary":true,"text":[{"text":"fn when_device_online_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":58,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":12261,"byte_end":12270,"line_start":294,"line_end":294,"column_start":58,"column_end":67,"is_primary":true,"text":[{"text":"fn when_device_online_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":58,"highlight_end":67}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:294:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn when_device_online_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `uhsd_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1945,"byte_end":1957,"line_start":41,"line_end":41,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    uhsd_manager: String,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1945,"byte_end":1957,"line_start":41,"line_end":41,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    uhsd_manager: String,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":"_uhsd_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `uhsd_manager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:41:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    uhsd_manager: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_uhsd_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `uhsd_device_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1971,"byte_end":1990,"line_start":42,"line_end":42,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    uhsd_device_manager: String,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1971,"byte_end":1990,"line_start":42,"line_end":42,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    uhsd_device_manager: String,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":"_uhsd_device_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `uhsd_device_manager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:42:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    uhsd_device_manager: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_uhsd_device_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":2801,"byte_end":2807,"line_start":63,"line_end":63,"column_start":63,"column_end":69,"is_primary":true,"text":[{"text":"fn mock_lib_uhome_subscribe_device_list(_world: &mut MyWorld, result: String) {","highlight_start":63,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":2801,"byte_end":2807,"line_start":63,"line_end":63,"column_start":63,"column_end":69,"is_primary":true,"text":[{"text":"fn mock_lib_uhome_subscribe_device_list(_world: &mut MyWorld, result: String) {","highlight_start":63,"highlight_end":69}],"label":null,"suggested_replacement":"_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `result`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:63:63\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn mock_lib_uhome_subscribe_device_list(_world: &mut MyWorld, result: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_result`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `step`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":13111,"byte_end":13115,"line_start":346,"line_end":346,"column_start":72,"column_end":76,"is_primary":true,"text":[{"text":"fn check_uhsdk_login_called_times(_world: &mut MyWorld, times: String, step: &Step) {","highlight_start":72,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":13111,"byte_end":13115,"line_start":346,"line_end":346,"column_start":72,"column_end":76,"is_primary":true,"text":[{"text":"fn check_uhsdk_login_called_times(_world: &mut MyWorld, times: String, step: &Step) {","highlight_start":72,"highlight_end":76}],"label":null,"suggested_replacement":"_step","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `step`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:346:72\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m346\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn check_uhsdk_login_called_times(_world: &mut MyWorld, times: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_step`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":19993,"byte_end":19995,"line_start":523,"line_end":523,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":19993,"byte_end":19995,"line_start":523,"line_end":523,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:523:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m523\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                device_id: id,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":21196,"byte_end":21198,"line_start":553,"line_end":553,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":21196,"byte_end":21198,"line_start":553,"line_end":553,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:553:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m553\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                device_id: id,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `connect_state` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":22241,"byte_end":22254,"line_start":581,"line_end":581,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"    let mut connect_state = DeviceConnectState::UHSD_USR_DEV_CONNECT_STATE_DISCONNECTED;","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_connect_state` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable `connect_state` is assigned to, but never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:581:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m581\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut connect_state = DeviceConnectState::UHSD_USR_DEV_CONNECT_STATE_DISCONNECTED;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `_connect_state` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `connect_state` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":22363,"byte_end":22376,"line_start":583,"line_end":583,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"        connect_state = DeviceConnectState::UHSD_USR_DEV_CONNECT_STATE_READY;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `connect_state` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:583:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m583\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        connect_state = DeviceConnectState::UHSD_USR_DEV_CONNECT_STATE_READY;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":22672,"byte_end":22674,"line_start":589,"line_end":589,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":22672,"byte_end":22674,"line_start":589,"line_end":589,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:589:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m589\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                device_id: id,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `online_state` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":24164,"byte_end":24176,"line_start":630,"line_end":630,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"    let mut online_state = UpDeviceOnlineState::Offline;","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_online_state` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable `online_state` is assigned to, but never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:630:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m630\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut online_state = UpDeviceOnlineState::Offline;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `_online_state` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `online_state` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":24251,"byte_end":24263,"line_start":632,"line_end":632,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        online_state = UpDeviceOnlineState::Online;","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `online_state` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:632:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m632\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        online_state = UpDeviceOnlineState::Online;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":24533,"byte_end":24535,"line_start":638,"line_end":638,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":24533,"byte_end":24535,"line_start":638,"line_end":638,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:638:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m638\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                device_id: id,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 14 previous errors; 72 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 14 previous errors; 72 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0061, E0308, E0382, E0599, E0600, E0615.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0061, E0308, E0382, E0599, E0600, E0615.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0061`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0061`.\u001b[0m\n"}
