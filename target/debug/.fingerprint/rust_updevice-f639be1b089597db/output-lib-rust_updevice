{"$message_type":"diagnostic","message":"unused variable: `device_ids`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/src/api/device_manager.rs","byte_start":22987,"byte_end":22997,"line_start":619,"line_end":619,"column_start":43,"column_end":53,"is_primary":true,"text":[{"text":"            ToolkitEvent::DevicesRemoved {device_ids} => {","highlight_start":43,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/src/api/device_manager.rs","byte_start":22987,"byte_end":22997,"line_start":619,"line_end":619,"column_start":43,"column_end":53,"is_primary":true,"text":[{"text":"            ToolkitEvent::DevicesRemoved {device_ids} => {","highlight_start":43,"highlight_end":53}],"label":null,"suggested_replacement":"device_ids: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_ids`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/api/device_manager.rs:619:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m619\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ToolkitEvent::DevicesRemoved {device_ids} => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `device_ids: _`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `logic_engine` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/src/daemon/extend_api_prepare.rs","byte_start":4427,"byte_end":4447,"line_start":140,"line_end":140,"column_start":12,"column_end":32,"is_primary":false,"text":[{"text":"pub struct DefaultDevicePrepare {","highlight_start":12,"highlight_end":32}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/src/daemon/extend_api_prepare.rs","byte_start":4454,"byte_end":4466,"line_start":141,"line_end":141,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    logic_engine: Arc<RwLock<Option<LogicEngine>>>,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `logic_engine` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/daemon/extend_api_prepare.rs:141:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m140\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DefaultDevicePrepare {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    logic_engine: Arc<RwLock<Option<LogicEngine>>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `SUCCESS` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/src/features/constant.rs","byte_start":53,"byte_end":60,"line_start":3,"line_end":3,"column_start":22,"column_end":29,"is_primary":true,"text":[{"text":"    pub(crate) const SUCCESS: i32 = 0;","highlight_start":22,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `SUCCESS` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/features/constant.rs:3:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) const SUCCESS: i32 = 0;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `RESULT_STRING_VALUE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/src/features/ffi_models.rs","byte_start":655,"byte_end":674,"line_start":16,"line_end":16,"column_start":11,"column_end":30,"is_primary":true,"text":[{"text":"pub const RESULT_STRING_VALUE: &str = \"stringValue\";","highlight_start":11,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `RESULT_STRING_VALUE` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/features/ffi_models.rs:16:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub const RESULT_STRING_VALUE: &str = \"stringValue\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `RESULT_BOOLEAN_VALUE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/src/features/ffi_models.rs","byte_start":708,"byte_end":728,"line_start":17,"line_end":17,"column_start":11,"column_end":31,"is_primary":true,"text":[{"text":"pub const RESULT_BOOLEAN_VALUE: &str = \"booleanValue\";","highlight_start":11,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `RESULT_BOOLEAN_VALUE` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/features/ffi_models.rs:17:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub const RESULT_BOOLEAN_VALUE: &str = \"booleanValue\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `RESULT_NUMBER_VALUE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/src/features/ffi_models.rs","byte_start":763,"byte_end":782,"line_start":18,"line_end":18,"column_start":11,"column_end":30,"is_primary":true,"text":[{"text":"pub const RESULT_NUMBER_VALUE: &str = \"numberValue\";","highlight_start":11,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `RESULT_NUMBER_VALUE` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/features/ffi_models.rs:18:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub const RESULT_NUMBER_VALUE: &str = \"numberValue\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `set_engine_attribute_list` and `set_engine_caution_list` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/src/features/ffi_models.rs","byte_start":1573,"byte_end":1591,"line_start":41,"line_end":41,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl UpDeviceModel {","highlight_start":1,"highlight_end":19}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/src/features/ffi_models.rs","byte_start":1605,"byte_end":1630,"line_start":42,"line_end":42,"column_start":12,"column_end":37,"is_primary":true,"text":[{"text":"    pub fn set_engine_attribute_list(&mut self, attribute_list: Vec<UpDeviceAttributeModel>) {","highlight_start":12,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/src/features/ffi_models.rs","byte_start":1760,"byte_end":1783,"line_start":46,"line_end":46,"column_start":12,"column_end":35,"is_primary":true,"text":[{"text":"    pub fn set_engine_caution_list(&mut self, caution_list: Vec<LECaution>) {","highlight_start":12,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `set_engine_attribute_list` and `set_engine_caution_list` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/features/ffi_models.rs:42:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl UpDeviceModel {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_engine_attribute_list(&mut self, attribute_lis\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m46\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_engine_caution_list(&mut self, caution_list: V\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"7 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 7 warnings emitted\u001b[0m\n\n"}
