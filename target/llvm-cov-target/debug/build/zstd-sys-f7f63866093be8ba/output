cargo:rerun-if-env-changed=ZSTD_SYS_USE_PKG_CONFIG
OUT_DIR = Some(/Users/<USER>/repo_Uplus/target/llvm-cov-target/debug/build/zstd-sys-f7f63866093be8ba/out)
OPT_LEVEL = Some(0)
TARGET = Some(aarch64-apple-darwin)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_aarch64-apple-darwin
CC_aarch64-apple-darwin = None
cargo:rerun-if-env-changed=CC_aarch64_apple_darwin
CC_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
cargo:rerun-if-env-changed=MACOSX_DEPLOYMENT_TARGET
MACOSX_DEPLOYMENT_TARGET = None
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_aarch64_apple_darwin
CFLAGS_aarch64_apple_darwin = Some( -fprofile-instr-generate -fcoverage-mapping -fprofile-update=atomic)
cargo:rerun-if-env-changed=CC_SHELL_ESCAPED_FLAGS
CC_SHELL_ESCAPED_FLAGS = None
cargo:rerun-if-env-changed=CFLAGS_aarch64-apple-darwin
CFLAGS_aarch64-apple-darwin = None
CARGO_ENCODED_RUSTFLAGS = Some(-Cinstrument-coverage--cfg=coverage--cfg=trybuild_no_target)
OUT_DIR = Some(/Users/<USER>/repo_Uplus/target/llvm-cov-target/debug/build/zstd-sys-f7f63866093be8ba/out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=MACOSX_DEPLOYMENT_TARGET
MACOSX_DEPLOYMENT_TARGET = None
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_aarch64_apple_darwin
CFLAGS_aarch64_apple_darwin = Some( -fprofile-instr-generate -fcoverage-mapping -fprofile-update=atomic)
cargo:rerun-if-env-changed=CC_SHELL_ESCAPED_FLAGS
CC_SHELL_ESCAPED_FLAGS = None
cargo:rerun-if-env-changed=CFLAGS_aarch64-apple-darwin
CFLAGS_aarch64-apple-darwin = None
OUT_DIR = Some(/Users/<USER>/repo_Uplus/target/llvm-cov-target/debug/build/zstd-sys-f7f63866093be8ba/out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=MACOSX_DEPLOYMENT_TARGET
MACOSX_DEPLOYMENT_TARGET = None
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_aarch64_apple_darwin
CFLAGS_aarch64_apple_darwin = Some( -fprofile-instr-generate -fcoverage-mapping -fprofile-update=atomic)
cargo:rerun-if-env-changed=CC_SHELL_ESCAPED_FLAGS
CC_SHELL_ESCAPED_FLAGS = None
cargo:rerun-if-env-changed=CFLAGS_aarch64-apple-darwin
CFLAGS_aarch64-apple-darwin = None
OUT_DIR = Some(/Users/<USER>/repo_Uplus/target/llvm-cov-target/debug/build/zstd-sys-f7f63866093be8ba/out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=MACOSX_DEPLOYMENT_TARGET
MACOSX_DEPLOYMENT_TARGET = None
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_aarch64_apple_darwin
CFLAGS_aarch64_apple_darwin = Some( -fprofile-instr-generate -fcoverage-mapping -fprofile-update=atomic)
cargo:rerun-if-env-changed=CC_SHELL_ESCAPED_FLAGS
CC_SHELL_ESCAPED_FLAGS = None
cargo:rerun-if-env-changed=CFLAGS_aarch64-apple-darwin
CFLAGS_aarch64-apple-darwin = None
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
cargo:rerun-if-env-changed=AR_aarch64-apple-darwin
AR_aarch64-apple-darwin = None
cargo:rerun-if-env-changed=AR_aarch64_apple_darwin
AR_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_aarch64_apple_darwin
ARFLAGS_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=ARFLAGS_aarch64-apple-darwin
ARFLAGS_aarch64-apple-darwin = None
cargo:rustc-link-lib=static=zstd
cargo:rustc-link-search=native=/Users/<USER>/repo_Uplus/target/llvm-cov-target/debug/build/zstd-sys-f7f63866093be8ba/out
cargo:root=/Users/<USER>/repo_Uplus/target/llvm-cov-target/debug/build/zstd-sys-f7f63866093be8ba/out
cargo:include=/Users/<USER>/.cargo/registry/src/mirrors.ustc.edu.cn-61ef6e0cd06fb9b8/zstd-sys-2.0.15+zstd.1.5.7/zstd/lib
