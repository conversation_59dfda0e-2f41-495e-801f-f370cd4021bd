const AF : Keywords < 'static > = Keywords { feature : & ["Funksie" , "<PERSON><PERSON><PERSON><PERSON><PERSON>fte" , "Ver<PERSON>ë"] , background : & ["Agtergrond"] , rule : & ["Regel"] , scenario : & ["Voorbeeld" , "Situasie"] , scenario_outline : & ["Situasie Uiteensetting"] , examples : & ["Voorbeelde"] , given : & ["* " , "Gegewe "] , when : & ["* " , "Wanneer "] , then : & ["* " , "<PERSON> "] , and : & ["* " , "En "] , but : & ["* " , "Maar "] , } ; const AM : Keywords < 'static > = Keywords { feature : & ["Ֆունկցիոնալություն" , "Հատկություն"] , background : & ["Կոնտեքստ"] , rule : & ["Rule"] , scenario : & ["Օրինակ" , "Սցենար"] , scenario_outline : & ["Սցենարի կառուցվացքը"] , examples : & ["Օրինակներ"] , given : & ["* " , "Դիցուք "] , when : & ["* " , "Եթե " , "Երբ "] , then : & ["* " , "Ապա "] , and : & ["* " , "Եվ "] , but : & ["* " , "Բայց "] , } ; const AN : Keywords < 'static > = Keywords { feature : & ["Caracteristica"] , background : & ["Antecedents"] , rule : & ["Rule"] , scenario : & ["Eixemplo" , "Caso"] , scenario_outline : & ["Esquema del caso"] , examples : & ["Eixemplos"] , given : & ["* " , "Dau " , "Dada " , "Daus " , "Dadas "] , when : & ["* " , "Cuan "] , then : & ["* " , "Alavez " , "Allora " , "Antonces "] , and : & ["* " , "Y " , "E "] , but : & ["* " , "Pero "] , } ; const AR : Keywords < 'static > = Keywords { feature : & ["خاصية"] , background : & ["الخلفية"] , rule : & ["Rule"] , scenario : & ["مثال" , "سيناريو"] , scenario_outline : & ["سيناريو مخطط"] , examples : & ["امثلة"] , given : & ["* " , "بفرض "] , when : & ["* " , "متى " , "عندما "] , then : & ["* " , "اذا\u{64b} " , "ثم "] , and : & ["* " , "و "] , but : & ["* " , "لكن "] , } ; const AST : Keywords < 'static > = Keywords { feature : & ["Carauterística"] , background : & ["Antecedentes"] , rule : & ["Rule"] , scenario : & ["Exemplo" , "Casu"] , scenario_outline : & ["Esbozu del casu"] , examples : & ["Exemplos"] , given : & ["* " , "Dáu " , "Dada " , "Daos " , "Daes "] , when : & ["* " , "Cuando "] , then : & ["* " , "Entós "] , and : & ["* " , "Y " , "Ya "] , but : & ["* " , "Peru "] , } ; const AZ : Keywords < 'static > = Keywords { feature : & ["Özəllik"] , background : & ["Keçmiş" , "Kontekst"] , rule : & ["Rule"] , scenario : & ["Nümunə" , "Ssenari"] , scenario_outline : & ["Ssenarinin strukturu"] , examples : & ["Nümunələr"] , given : & ["* " , "Tutaq ki " , "Verilir "] , when : & ["* " , "Əgər " , "Nə vaxt ki "] , then : & ["* " , "O halda "] , and : & ["* " , "Və " , "Həm "] , but : & ["* " , "Amma " , "Ancaq "] , } ; const BG : Keywords < 'static > = Keywords { feature : & ["Функционалност"] , background : & ["Предистория"] , rule : & ["Правило"] , scenario : & ["Пример" , "Сценарий"] , scenario_outline : & ["Рамка на сценарий"] , examples : & ["Примери"] , given : & ["* " , "Дадено "] , when : & ["* " , "Когато "] , then : & ["* " , "То "] , and : & ["* " , "И "] , but : & ["* " , "Но "] , } ; const BM : Keywords < 'static > = Keywords { feature : & ["Fungsi"] , background : & ["Latar Belakang"] , rule : & ["Rule"] , scenario : & ["Senario" , "Situasi" , "Keadaan"] , scenario_outline : & ["Kerangka Senario" , "Kerangka Situasi" , "Kerangka Keadaan" , "Garis Panduan Senario"] , examples : & ["Contoh"] , given : & ["* " , "Diberi " , "Bagi "] , when : & ["* " , "Apabila "] , then : & ["* " , "Maka " , "Kemudian "] , and : & ["* " , "Dan "] , but : & ["* " , "Tetapi " , "Tapi "] , } ; const BS : Keywords < 'static > = Keywords { feature : & ["Karakteristika"] , background : & ["Pozadina"] , rule : & ["Rule"] , scenario : & ["Primjer" , "Scenariju" , "Scenario"] , scenario_outline : & ["Scenariju-obris" , "Scenario-outline"] , examples : & ["Primjeri"] , given : & ["* " , "Dato "] , when : & ["* " , "Kada "] , then : & ["* " , "Zatim "] , and : & ["* " , "I " , "A "] , but : & ["* " , "Ali "] , } ; const CA : Keywords < 'static > = Keywords { feature : & ["Característica" , "Funcionalitat"] , background : & ["Rerefons" , "Antecedents"] , rule : & ["Rule"] , scenario : & ["Exemple" , "Escenari"] , scenario_outline : & ["Esquema de l'escenari"] , examples : & ["Exemples"] , given : & ["* " , "Donat " , "Donada " , "Atès " , "Atesa "] , when : & ["* " , "Quan "] , then : & ["* " , "Aleshores " , "Cal "] , and : & ["* " , "I "] , but : & ["* " , "Però "] , } ; const CS : Keywords < 'static > = Keywords { feature : & ["Požadavek"] , background : & ["Pozadí" , "Kontext"] , rule : & ["Pravidlo"] , scenario : & ["Příklad" , "Scénář"] , scenario_outline : & ["Náčrt Scénáře" , "Osnova scénáře"] , examples : & ["Příklady"] , given : & ["* " , "Pokud " , "Za předpokladu "] , when : & ["* " , "Když "] , then : & ["* " , "Pak "] , and : & ["* " , "A také " , "A "] , but : & ["* " , "Ale "] , } ; const CY_GB : Keywords < 'static > = Keywords { feature : & ["Arwedd"] , background : & ["Cefndir"] , rule : & ["Rule"] , scenario : & ["Enghraifft" , "Scenario"] , scenario_outline : & ["Scenario Amlinellol"] , examples : & ["Enghreifftiau"] , given : & ["* " , "Anrhegedig a "] , when : & ["* " , "Pryd "] , then : & ["* " , "Yna "] , and : & ["* " , "A "] , but : & ["* " , "Ond "] , } ; const DA : Keywords < 'static > = Keywords { feature : & ["Egenskab"] , background : & ["Baggrund"] , rule : & ["Rule"] , scenario : & ["Eksempel" , "Scenarie"] , scenario_outline : & ["Abstrakt Scenario"] , examples : & ["Eksempler"] , given : & ["* " , "Givet "] , when : & ["* " , "Når "] , then : & ["* " , "Så "] , and : & ["* " , "Og "] , but : & ["* " , "Men "] , } ; const DE : Keywords < 'static > = Keywords { feature : & ["Funktionalität" , "Funktion"] , background : & ["Grundlage" , "Hintergrund" , "Voraussetzungen" , "Vorbedingungen"] , rule : & ["Rule" , "Regel"] , scenario : & ["Beispiel" , "Szenario"] , scenario_outline : & ["Szenariogrundriss" , "Szenarien"] , examples : & ["Beispiele"] , given : & ["* " , "Angenommen " , "Gegeben sei " , "Gegeben seien "] , when : & ["* " , "Wenn "] , then : & ["* " , "Dann "] , and : & ["* " , "Und "] , but : & ["* " , "Aber "] , } ; const EL : Keywords < 'static > = Keywords { feature : & ["Δυνατότητα" , "Λειτουργία"] , background : & ["Υπόβαθρο"] , rule : & ["Rule"] , scenario : & ["Παράδειγμα" , "Σενάριο"] , scenario_outline : & ["Περιγραφή Σεναρίου" , "Περίγραμμα Σεναρίου"] , examples : & ["Παραδείγματα" , "Σενάρια"] , given : & ["* " , "Δεδομένου "] , when : & ["* " , "Όταν "] , then : & ["* " , "Τότε "] , and : & ["* " , "Και "] , but : & ["* " , "Αλλά "] , } ; const EM : Keywords < 'static > = Keywords { feature : & ["📚"] , background : & ["💤"] , rule : & ["Rule"] , scenario : & ["🥒" , "📕"] , scenario_outline : & ["📖"] , examples : & ["📓"] , given : & ["* " , "😐"] , when : & ["* " , "🎬"] , then : & ["* " , "🙏"] , and : & ["* " , "😂"] , but : & ["* " , "😔"] , } ; const EN : Keywords < 'static > = Keywords { feature : & ["Feature" , "Business Need" , "Ability"] , background : & ["Background"] , rule : & ["Rule"] , scenario : & ["Example" , "Scenario"] , scenario_outline : & ["Scenario Outline" , "Scenario Template"] , examples : & ["Examples" , "Scenarios"] , given : & ["* " , "Given "] , when : & ["* " , "When "] , then : & ["* " , "Then "] , and : & ["* " , "And "] , but : & ["* " , "But "] , } ; const EN_SCOUSE : Keywords < 'static > = Keywords { feature : & ["Feature"] , background : & ["Dis is what went down"] , rule : & ["Rule"] , scenario : & ["The thing of it is"] , scenario_outline : & ["Wharrimean is"] , examples : & ["Examples"] , given : & ["* " , "Givun " , "Youse know when youse got "] , when : & ["* " , "Wun " , "Youse know like when "] , then : & ["* " , "Dun " , "Den youse gotta "] , and : & ["* " , "An "] , but : & ["* " , "Buh "] , } ; const EN_AU : Keywords < 'static > = Keywords { feature : & ["Pretty much"] , background : & ["First off"] , rule : & ["Rule"] , scenario : & ["Awww, look mate"] , scenario_outline : & ["Reckon it's like"] , examples : & ["You'll wanna"] , given : & ["* " , "Y'know "] , when : & ["* " , "It's just unbelievable "] , then : & ["* " , "But at the end of the day I reckon "] , and : & ["* " , "Too right "] , but : & ["* " , "Yeah nah "] , } ; const EN_LOL : Keywords < 'static > = Keywords { feature : & ["OH HAI"] , background : & ["B4"] , rule : & ["Rule"] , scenario : & ["MISHUN"] , scenario_outline : & ["MISHUN SRSLY"] , examples : & ["EXAMPLZ"] , given : & ["* " , "I CAN HAZ "] , when : & ["* " , "WEN "] , then : & ["* " , "DEN "] , and : & ["* " , "AN "] , but : & ["* " , "BUT "] , } ; const EN_OLD : Keywords < 'static > = Keywords { feature : & ["Hwaet" , "Hwæt"] , background : & ["Aer" , "Ær"] , rule : & ["Rule"] , scenario : & ["Swa"] , scenario_outline : & ["Swa hwaer swa" , "Swa hwær swa"] , examples : & ["Se the" , "Se þe" , "Se ðe"] , given : & ["* " , "Thurh " , "Þurh " , "Ðurh "] , when : & ["* " , "Tha " , "Þa " , "Ða "] , then : & ["* " , "Tha " , "Þa " , "Ða " , "Tha the " , "Þa þe " , "Ða ðe "] , and : & ["* " , "Ond " , "7 "] , but : & ["* " , "Ac "] , } ; const EN_PIRATE : Keywords < 'static > = Keywords { feature : & ["Ahoy matey!"] , background : & ["Yo-ho-ho"] , rule : & ["Rule"] , scenario : & ["Heave to"] , scenario_outline : & ["Shiver me timbers"] , examples : & ["Dead men tell no tales"] , given : & ["* " , "Gangway! "] , when : & ["* " , "Blimey! "] , then : & ["* " , "Let go and haul "] , and : & ["* " , "Aye "] , but : & ["* " , "Avast! "] , } ; const EN_TX : Keywords < 'static > = Keywords { feature : & ["This ain’t my first rodeo" , "All gussied up"] , background : & ["Lemme tell y'all a story"] , rule : & ["Rule "] , scenario : & ["All hat and no cattle"] , scenario_outline : & ["Serious as a snake bite" , "Busy as a hound in flea season"] , examples : & ["Now that's a story longer than a cattle drive in July"] , given : & ["Fixin' to " , "All git out "] , when : & ["Quick out of the chute "] , then : & ["There’s no tree but bears some fruit "] , and : & ["Come hell or high water "] , but : & ["Well now hold on, I'll you what "] , } ; const EO : Keywords < 'static > = Keywords { feature : & ["Trajto"] , background : & ["Fono"] , rule : & ["Rule"] , scenario : & ["Ekzemplo" , "Scenaro" , "Kazo"] , scenario_outline : & ["Konturo de la scenaro" , "Skizo" , "Kazo-skizo"] , examples : & ["Ekzemploj"] , given : & ["* " , "Donitaĵo " , "Komence "] , when : & ["* " , "Se "] , then : & ["* " , "Do "] , and : & ["* " , "Kaj "] , but : & ["* " , "Sed "] , } ; const ES : Keywords < 'static > = Keywords { feature : & ["Característica" , "Necesidad del negocio" , "Requisito"] , background : & ["Antecedentes"] , rule : & ["Regla" , "Regla de negocio"] , scenario : & ["Ejemplo" , "Escenario"] , scenario_outline : & ["Esquema del escenario"] , examples : & ["Ejemplos"] , given : & ["* " , "Dado " , "Dada " , "Dados " , "Dadas "] , when : & ["* " , "Cuando "] , then : & ["* " , "Entonces "] , and : & ["* " , "Y " , "E "] , but : & ["* " , "Pero "] , } ; const ET : Keywords < 'static > = Keywords { feature : & ["Omadus"] , background : & ["Taust"] , rule : & ["Reegel"] , scenario : & ["Juhtum" , "Stsenaarium"] , scenario_outline : & ["Raamjuhtum" , "Raamstsenaarium"] , examples : & ["Juhtumid"] , given : & ["* " , "Eeldades "] , when : & ["* " , "Kui "] , then : & ["* " , "Siis "] , and : & ["* " , "Ja "] , but : & ["* " , "Kuid "] , } ; const FA : Keywords < 'static > = Keywords { feature : & ["و\u{650}یژگی"] , background : & ["زمینه"] , rule : & ["Rule"] , scenario : & ["مثال" , "سناریو"] , scenario_outline : & ["الگوی سناریو"] , examples : & ["نمونه ها"] , given : & ["* " , "با فرض "] , when : & ["* " , "هنگامی "] , then : & ["* " , "آنگاه "] , and : & ["* " , "و "] , but : & ["* " , "اما "] , } ; const FI : Keywords < 'static > = Keywords { feature : & ["Ominaisuus"] , background : & ["Tausta"] , rule : & ["Rule"] , scenario : & ["Tapaus"] , scenario_outline : & ["Tapausaihio"] , examples : & ["Tapaukset"] , given : & ["* " , "Oletetaan "] , when : & ["* " , "Kun "] , then : & ["* " , "Niin "] , and : & ["* " , "Ja "] , but : & ["* " , "Mutta "] , } ; const FR : Keywords < 'static > = Keywords { feature : & ["Fonctionnalité"] , background : & ["Contexte"] , rule : & ["Règle"] , scenario : & ["Exemple" , "Scénario"] , scenario_outline : & ["Plan du scénario" , "Plan du Scénario"] , examples : & ["Exemples"] , given : & ["* " , "Soit " , "Sachant que " , "Sachant qu'" , "Sachant " , "Etant donné que " , "Etant donné qu'" , "Etant donné " , "Etant donnée " , "Etant donnés " , "Etant données " , "Étant donné que " , "Étant donné qu'" , "Étant donné " , "Étant donnée " , "Étant donnés " , "Étant données "] , when : & ["* " , "Quand " , "Lorsque " , "Lorsqu'"] , then : & ["* " , "Alors " , "Donc "] , and : & ["* " , "Et que " , "Et qu'" , "Et "] , but : & ["* " , "Mais que " , "Mais qu'" , "Mais "] , } ; const GA : Keywords < 'static > = Keywords { feature : & ["Gné"] , background : & ["Cúlra"] , rule : & ["Rule"] , scenario : & ["Sampla" , "Cás"] , scenario_outline : & ["Cás Achomair"] , examples : & ["Samplaí"] , given : & ["* " , "Cuir i gcás go" , "Cuir i gcás nach" , "Cuir i gcás gur" , "Cuir i gcás nár"] , when : & ["* " , "Nuair a" , "Nuair nach" , "Nuair ba" , "Nuair nár"] , then : & ["* " , "Ansin"] , and : & ["* " , "Agus"] , but : & ["* " , "Ach"] , } ; const GJ : Keywords < 'static > = Keywords { feature : & ["લક\u{acd}ષણ" , "વ\u{acd}યાપાર જર\u{ac2}ર" , "ક\u{acd}ષમતા"] , background : & ["બ\u{ac7}કગ\u{acd}રાઉન\u{acd}ડ"] , rule : & ["Rule"] , scenario : & ["ઉદાહરણ" , "સ\u{acd}થિતિ"] , scenario_outline : & ["પરિદ\u{acd}દશ\u{acd}ય ર\u{ac2}પર\u{ac7}ખા" , "પરિદ\u{acd}દશ\u{acd}ય ઢા\u{a82}ચો"] , examples : & ["ઉદાહરણો"] , given : & ["* " , "આપ\u{ac7}લ છ\u{ac7} "] , when : & ["* " , "ક\u{acd}યાર\u{ac7} "] , then : & ["* " , "પછી "] , and : & ["* " , "અન\u{ac7} "] , but : & ["* " , "પણ "] , } ; const GL : Keywords < 'static > = Keywords { feature : & ["Característica"] , background : & ["Contexto"] , rule : & ["Rule"] , scenario : & ["Exemplo" , "Escenario"] , scenario_outline : & ["Esbozo do escenario"] , examples : & ["Exemplos"] , given : & ["* " , "Dado " , "Dada " , "Dados " , "Dadas "] , when : & ["* " , "Cando "] , then : & ["* " , "Entón " , "Logo "] , and : & ["* " , "E "] , but : & ["* " , "Mais " , "Pero "] , } ; const HE : Keywords < 'static > = Keywords { feature : & ["תכונה"] , background : & ["רקע"] , rule : & ["כלל"] , scenario : & ["דוגמא" , "תרחיש"] , scenario_outline : & ["תבנית תרחיש"] , examples : & ["דוגמאות"] , given : & ["* " , "בהינתן "] , when : & ["* " , "כאשר "] , then : & ["* " , "אז " , "אזי "] , and : & ["* " , "וגם "] , but : & ["* " , "אבל "] , } ; const HI : Keywords < 'static > = Keywords { feature : & ["र\u{942}प ल\u{947}ख"] , background : & ["प\u{943}ष\u{94d}ठभ\u{942}मि"] , rule : & ["नियम"] , scenario : & ["परिद\u{943}श\u{94d}य"] , scenario_outline : & ["परिद\u{943}श\u{94d}य र\u{942}पर\u{947}खा"] , examples : & ["उदाहरण"] , given : & ["* " , "अगर " , "यदि " , "च\u{942}\u{902}कि "] , when : & ["* " , "जब " , "कदा "] , then : & ["* " , "तब " , "तदा "] , and : & ["* " , "और " , "तथा "] , but : & ["* " , "पर " , "परन\u{94d}त\u{941} " , "किन\u{94d}त\u{941} "] , } ; const HR : Keywords < 'static > = Keywords { feature : & ["Osobina" , "Mogućnost" , "Mogucnost"] , background : & ["Pozadina"] , rule : & ["Rule"] , scenario : & ["Primjer" , "Scenarij"] , scenario_outline : & ["Skica" , "Koncept"] , examples : & ["Primjeri" , "Scenariji"] , given : & ["* " , "Zadan " , "Zadani " , "Zadano " , "Ukoliko "] , when : & ["* " , "Kada " , "Kad "] , then : & ["* " , "Onda "] , and : & ["* " , "I "] , but : & ["* " , "Ali "] , } ; const HT : Keywords < 'static > = Keywords { feature : & ["Karakteristik" , "Mak" , "Fonksyonalite"] , background : & ["Kontèks" , "Istorik"] , rule : & ["Rule"] , scenario : & ["Senaryo"] , scenario_outline : & ["Plan senaryo" , "Plan Senaryo" , "Senaryo deskripsyon" , "Senaryo Deskripsyon" , "Dyagram senaryo" , "Dyagram Senaryo"] , examples : & ["Egzanp"] , given : & ["* " , "Sipoze " , "Sipoze ke " , "Sipoze Ke "] , when : & ["* " , "Lè " , "Le "] , then : & ["* " , "Lè sa a " , "Le sa a "] , and : & ["* " , "Ak " , "Epi " , "E "] , but : & ["* " , "Men "] , } ; const HU : Keywords < 'static > = Keywords { feature : & ["Jellemző"] , background : & ["Háttér"] , rule : & ["Szabály"] , scenario : & ["Példa" , "Forgatókönyv"] , scenario_outline : & ["Forgatókönyv vázlat"] , examples : & ["Példák"] , given : & ["* " , "Amennyiben " , "Adott "] , when : & ["* " , "Majd " , "Ha " , "Amikor "] , then : & ["* " , "Akkor "] , and : & ["* " , "És "] , but : & ["* " , "De "] , } ; const ID : Keywords < 'static > = Keywords { feature : & ["Fitur"] , background : & ["Dasar" , "Latar Belakang"] , rule : & ["Rule" , "Aturan"] , scenario : & ["Skenario"] , scenario_outline : & ["Skenario konsep" , "Garis-Besar Skenario"] , examples : & ["Contoh" , "Misal"] , given : & ["* " , "Dengan " , "Diketahui " , "Diasumsikan " , "Bila " , "Jika "] , when : & ["* " , "Ketika "] , then : & ["* " , "Maka " , "Kemudian "] , and : & ["* " , "Dan "] , but : & ["* " , "Tapi " , "Tetapi "] , } ; const IS : Keywords < 'static > = Keywords { feature : & ["Eiginleiki"] , background : & ["Bakgrunnur"] , rule : & ["Rule"] , scenario : & ["Atburðarás"] , scenario_outline : & ["Lýsing Atburðarásar" , "Lýsing Dæma"] , examples : & ["Dæmi" , "Atburðarásir"] , given : & ["* " , "Ef "] , when : & ["* " , "Þegar "] , then : & ["* " , "Þá "] , and : & ["* " , "Og "] , but : & ["* " , "En "] , } ; const IT : Keywords < 'static > = Keywords { feature : & ["Funzionalità" , "Esigenza di Business" , "Abilità"] , background : & ["Contesto"] , rule : & ["Regola"] , scenario : & ["Esempio" , "Scenario"] , scenario_outline : & ["Schema dello scenario"] , examples : & ["Esempi"] , given : & ["* " , "Dato " , "Data " , "Dati " , "Date "] , when : & ["* " , "Quando "] , then : & ["* " , "Allora "] , and : & ["* " , "E "] , but : & ["* " , "Ma "] , } ; const JA : Keywords < 'static > = Keywords { feature : & ["フィーチャ" , "機能"] , background : & ["背景"] , rule : & ["Rule"] , scenario : & ["シナリオ"] , scenario_outline : & ["シナリオアウトライン" , "シナリオテンプレート" , "テンプレ" , "シナリオテンプレ"] , examples : & ["例" , "サンプル"] , given : & ["* " , "前提"] , when : & ["* " , "もし"] , then : & ["* " , "ならば"] , and : & ["* " , "かつ"] , but : & ["* " , "しかし" , "但し" , "ただし"] , } ; const JV : Keywords < 'static > = Keywords { feature : & ["Fitur"] , background : & ["Dasar"] , rule : & ["Rule"] , scenario : & ["Skenario"] , scenario_outline : & ["Konsep skenario"] , examples : & ["Conto" , "Contone"] , given : & ["* " , "Nalika " , "Nalikaning "] , when : & ["* " , "Manawa " , "Menawa "] , then : & ["* " , "Njuk " , "Banjur "] , and : & ["* " , "Lan "] , but : & ["* " , "Tapi " , "Nanging " , "Ananging "] , } ; const KA : Keywords < 'static > = Keywords { feature : & ["თვისება"] , background : & ["კონტექსტი"] , rule : & ["Rule"] , scenario : & ["მაგალითად" , "სცენარის"] , scenario_outline : & ["სცენარის ნიმუში"] , examples : & ["მაგალითები"] , given : & ["* " , "მოცემული"] , when : & ["* " , "როდესაც"] , then : & ["* " , "მაშინ"] , and : & ["* " , "და"] , but : & ["* " , "მაგ\u{ad}რამ"] , } ; const KN : Keywords < 'static > = Keywords { feature : & ["ಹ\u{cc6}ಚ\u{ccd}ಚಳ"] , background : & ["ಹ\u{cbf}ನ\u{ccd}ನ\u{cc6}ಲ\u{cc6}"] , rule : & ["Rule"] , scenario : & ["ಉದಾಹರಣ\u{cc6}" , "ಕಥಾಸಾರಾಂಶ"] , scenario_outline : & ["ವ\u{cbf}ವರಣ\u{cc6}"] , examples : & ["ಉದಾಹರಣ\u{cc6}ಗಳು"] , given : & ["* " , "ನ\u{cbf}\u{cd5}ಡ\u{cbf}ದ "] , when : & ["* " , "ಸ\u{ccd}ಥ\u{cbf}ತ\u{cbf}ಯನ\u{ccd}ನು "] , then : & ["* " , "ನಂತರ "] , and : & ["* " , "ಮತ\u{ccd}ತು "] , but : & ["* " , "ಆದರ\u{cc6} "] , } ; const KO : Keywords < 'static > = Keywords { feature : & ["기능"] , background : & ["배경"] , rule : & ["Rule"] , scenario : & ["시나리오"] , scenario_outline : & ["시나리오 개요"] , examples : & ["예"] , given : & ["* " , "조건" , "먼저"] , when : & ["* " , "만일" , "만약"] , then : & ["* " , "그러면"] , and : & ["* " , "그리고"] , but : & ["* " , "하지만" , "단"] , } ; const LT : Keywords < 'static > = Keywords { feature : & ["Savybė"] , background : & ["Kontekstas"] , rule : & ["Rule"] , scenario : & ["Pavyzdys" , "Scenarijus"] , scenario_outline : & ["Scenarijaus šablonas"] , examples : & ["Pavyzdžiai" , "Scenarijai" , "Variantai"] , given : & ["* " , "Duota "] , when : & ["* " , "Kai "] , then : & ["* " , "Tada "] , and : & ["* " , "Ir "] , but : & ["* " , "Bet "] , } ; const LU : Keywords < 'static > = Keywords { feature : & ["Funktionalitéit"] , background : & ["Hannergrond"] , rule : & ["Rule"] , scenario : & ["Beispill" , "Szenario"] , scenario_outline : & ["Plang vum Szenario"] , examples : & ["Beispiller"] , given : & ["* " , "ugeholl "] , when : & ["* " , "wann "] , then : & ["* " , "dann "] , and : & ["* " , "an " , "a "] , but : & ["* " , "awer " , "mä "] , } ; const LV : Keywords < 'static > = Keywords { feature : & ["Funkcionalitāte" , "Fīča"] , background : & ["Konteksts" , "Situācija"] , rule : & ["Rule"] , scenario : & ["Piemērs" , "Scenārijs"] , scenario_outline : & ["Scenārijs pēc parauga"] , examples : & ["Piemēri" , "Paraugs"] , given : & ["* " , "Kad "] , when : & ["* " , "Ja "] , then : & ["* " , "Tad "] , and : & ["* " , "Un "] , but : & ["* " , "Bet "] , } ; const MK_CYRL : Keywords < 'static > = Keywords { feature : & ["Функционалност" , "Бизнис потреба" , "Можност"] , background : & ["Контекст" , "Содржина"] , rule : & ["Rule"] , scenario : & ["Пример" , "Сценарио" , "На пример"] , scenario_outline : & ["Преглед на сценарија" , "Скица" , "Концепт"] , examples : & ["Примери" , "Сценарија"] , given : & ["* " , "Дадено " , "Дадена "] , when : & ["* " , "Кога "] , then : & ["* " , "Тогаш "] , and : & ["* " , "И "] , but : & ["* " , "Но "] , } ; const MK_LATN : Keywords < 'static > = Keywords { feature : & ["Funkcionalnost" , "Biznis potreba" , "Mozhnost"] , background : & ["Kontekst" , "Sodrzhina"] , rule : & ["Rule"] , scenario : & ["Scenario" , "Na primer"] , scenario_outline : & ["Pregled na scenarija" , "Skica" , "Koncept"] , examples : & ["Primeri" , "Scenaria"] , given : & ["* " , "Dadeno " , "Dadena "] , when : & ["* " , "Koga "] , then : & ["* " , "Togash "] , and : & ["* " , "I "] , but : & ["* " , "No "] , } ; const MN : Keywords < 'static > = Keywords { feature : & ["Функц" , "Функционал"] , background : & ["Агуулга"] , rule : & ["Rule"] , scenario : & ["Сценар"] , scenario_outline : & ["Сценарын төлөвлөгөө"] , examples : & ["Тухайлбал"] , given : & ["* " , "Өгөгдсөн нь " , "Анх "] , when : & ["* " , "Хэрэв "] , then : & ["* " , "Тэгэхэд " , "Үүний дараа "] , and : & ["* " , "Мөн " , "Тэгээд "] , but : & ["* " , "Гэхдээ " , "Харин "] , } ; const MR : Keywords < 'static > = Keywords { feature : & ["व\u{948}शिष\u{94d}ट\u{94d}य" , "स\u{941}विधा"] , background : & ["पार\u{94d}श\u{94d}वभ\u{942}मी"] , rule : & ["नियम"] , scenario : & ["परिद\u{943}श\u{94d}य"] , scenario_outline : & ["परिद\u{943}श\u{94d}य र\u{942}पर\u{947}खा"] , examples : & ["उदाहरण"] , given : & ["* " , "जर" , "दिल\u{947}ल\u{94d}या प\u{94d}रमाण\u{947} "] , when : & ["* " , "ज\u{947}व\u{94d}हा "] , then : & ["* " , "मग " , "त\u{947}व\u{94d}हा "] , and : & ["* " , "आणि " , "तस\u{947}च "] , but : & ["* " , "पण " , "पर\u{902}त\u{941} "] , } ; const NE : Keywords < 'static > = Keywords { feature : & ["स\u{941}विधा" , "विश\u{947}षता"] , background : & ["प\u{943}ष\u{94d}ठभ\u{942}मी"] , rule : & ["नियम"] , scenario : & ["परिद\u{943}श\u{94d}य"] , scenario_outline : & ["परिद\u{943}श\u{94d}य र\u{942}पर\u{947}खा"] , examples : & ["उदाहरण" , "उदाहरणहर\u{941}"] , given : & ["* " , "दिइएको " , "दिएको " , "यदि "] , when : & ["* " , "जब "] , then : & ["* " , "त\u{94d}यसपछि " , "अनी "] , and : & ["* " , "र " , "अनी "] , but : & ["* " , "तर "] , } ; const NL : Keywords < 'static > = Keywords { feature : & ["Functionaliteit"] , background : & ["Achtergrond"] , rule : & ["Rule"] , scenario : & ["Voorbeeld" , "Scenario"] , scenario_outline : & ["Abstract Scenario"] , examples : & ["Voorbeelden"] , given : & ["* " , "Gegeven " , "Stel "] , when : & ["* " , "Als " , "Wanneer "] , then : & ["* " , "Dan "] , and : & ["* " , "En "] , but : & ["* " , "Maar "] , } ; const NO : Keywords < 'static > = Keywords { feature : & ["Egenskap"] , background : & ["Bakgrunn"] , rule : & ["Regel"] , scenario : & ["Eksempel" , "Scenario"] , scenario_outline : & ["Scenariomal" , "Abstrakt Scenario"] , examples : & ["Eksempler"] , given : & ["* " , "Gitt "] , when : & ["* " , "Når "] , then : & ["* " , "Så "] , and : & ["* " , "Og "] , but : & ["* " , "Men "] , } ; const PA : Keywords < 'static > = Keywords { feature : & ["ਖਾਸੀਅਤ" , "ਮ\u{a41}ਹਾ\u{a02}ਦਰਾ" , "ਨਕਸ਼ ਨ\u{a41}ਹਾਰ"] , background : & ["ਪਿਛ\u{a4b}ਕੜ"] , rule : & ["Rule"] , scenario : & ["ਉਦਾਹਰਨ" , "ਪਟਕਥਾ"] , scenario_outline : & ["ਪਟਕਥਾ ਢਾ\u{a02}ਚਾ" , "ਪਟਕਥਾ ਰ\u{a42}ਪ ਰ\u{a47}ਖਾ"] , examples : & ["ਉਦਾਹਰਨਾ\u{a02}"] , given : & ["* " , "ਜ\u{a47}ਕਰ " , "ਜਿਵ\u{a47}\u{a02} ਕਿ "] , when : & ["* " , "ਜਦ\u{a4b}\u{a02} "] , then : & ["* " , "ਤਦ "] , and : & ["* " , "ਅਤ\u{a47} "] , but : & ["* " , "ਪਰ "] , } ; const PL : Keywords < 'static > = Keywords { feature : & ["Właściwość" , "Funkcja" , "Aspekt" , "Potrzeba biznesowa"] , background : & ["Założenia"] , rule : & ["Zasada" , "Reguła"] , scenario : & ["Przykład" , "Scenariusz"] , scenario_outline : & ["Szablon scenariusza"] , examples : & ["Przykłady"] , given : & ["* " , "Zakładając " , "Mając " , "Zakładając, że "] , when : & ["* " , "Jeżeli " , "Jeśli " , "Gdy " , "Kiedy "] , then : & ["* " , "Wtedy "] , and : & ["* " , "Oraz " , "I "] , but : & ["* " , "Ale "] , } ; const PT : Keywords < 'static > = Keywords { feature : & ["Funcionalidade" , "Característica" , "Caracteristica"] , background : & ["Contexto" , "Cenário de Fundo" , "Cenario de Fundo" , "Fundo"] , rule : & ["Regra"] , scenario : & ["Exemplo" , "Cenário" , "Cenario"] , scenario_outline : & ["Esquema do Cenário" , "Esquema do Cenario" , "Delineação do Cenário" , "Delineacao do Cenario"] , examples : & ["Exemplos" , "Cenários" , "Cenarios"] , given : & ["* " , "Dado " , "Dada " , "Dados " , "Dadas "] , when : & ["* " , "Quando "] , then : & ["* " , "Então " , "Entao "] , and : & ["* " , "E "] , but : & ["* " , "Mas "] , } ; const RO : Keywords < 'static > = Keywords { feature : & ["Functionalitate" , "Funcționalitate" , "Funcţionalitate"] , background : & ["Context"] , rule : & ["Rule"] , scenario : & ["Exemplu" , "Scenariu"] , scenario_outline : & ["Structura scenariu" , "Structură scenariu"] , examples : & ["Exemple"] , given : & ["* " , "Date fiind " , "Dat fiind " , "Dată fiind" , "Dati fiind " , "Dați fiind " , "Daţi fiind "] , when : & ["* " , "Cand " , "Când "] , then : & ["* " , "Atunci "] , and : & ["* " , "Si " , "Și " , "Şi "] , but : & ["* " , "Dar "] , } ; const RU : Keywords < 'static > = Keywords { feature : & ["Функция" , "Функциональность" , "Функционал" , "Свойство"] , background : & ["Предыстория" , "Контекст"] , rule : & ["Правило"] , scenario : & ["Пример" , "Сценарий"] , scenario_outline : & ["Структура сценария" , "Шаблон сценария"] , examples : & ["Примеры"] , given : & ["* " , "Допустим " , "Дано " , "Пусть "] , when : & ["* " , "Когда " , "Если "] , then : & ["* " , "То " , "Затем " , "Тогда "] , and : & ["* " , "И " , "К тому же " , "Также "] , but : & ["* " , "Но " , "А " , "Иначе "] , } ; const SK : Keywords < 'static > = Keywords { feature : & ["Požiadavka" , "Funkcia" , "Vlastnosť"] , background : & ["Pozadie"] , rule : & ["Rule"] , scenario : & ["Príklad" , "Scenár"] , scenario_outline : & ["Náčrt Scenáru" , "Náčrt Scenára" , "Osnova Scenára"] , examples : & ["Príklady"] , given : & ["* " , "Pokiaľ " , "Za predpokladu "] , when : & ["* " , "Keď " , "Ak "] , then : & ["* " , "Tak " , "Potom "] , and : & ["* " , "A " , "A tiež " , "A taktiež " , "A zároveň "] , but : & ["* " , "Ale "] , } ; const SL : Keywords < 'static > = Keywords { feature : & ["Funkcionalnost" , "Funkcija" , "Možnosti" , "Moznosti" , "Lastnost" , "Značilnost"] , background : & ["Kontekst" , "Osnova" , "Ozadje"] , rule : & ["Rule"] , scenario : & ["Primer" , "Scenarij"] , scenario_outline : & ["Struktura scenarija" , "Skica" , "Koncept" , "Oris scenarija" , "Osnutek"] , examples : & ["Primeri" , "Scenariji"] , given : & ["Dano " , "Podano " , "Zaradi " , "Privzeto "] , when : & ["Ko " , "Ce " , "Če " , "Kadar "] , then : & ["Nato " , "Potem " , "Takrat "] , and : & ["In " , "Ter "] , but : & ["Toda " , "Ampak " , "Vendar "] , } ; const SR_CYRL : Keywords < 'static > = Keywords { feature : & ["Функционалност" , "Могућност" , "Особина"] , background : & ["Контекст" , "Основа" , "Позадина"] , rule : & ["Правило"] , scenario : & ["Пример" , "Сценарио" , "Пример"] , scenario_outline : & ["Структура сценарија" , "Скица" , "Концепт"] , examples : & ["Примери" , "Сценарији"] , given : & ["* " , "За дато " , "За дате " , "За дати "] , when : & ["* " , "Када " , "Кад "] , then : & ["* " , "Онда "] , and : & ["* " , "И "] , but : & ["* " , "Али "] , } ; const SR_LATN : Keywords < 'static > = Keywords { feature : & ["Funkcionalnost" , "Mogućnost" , "Mogucnost" , "Osobina"] , background : & ["Kontekst" , "Osnova" , "Pozadina"] , rule : & ["Pravilo"] , scenario : & ["Scenario" , "Primer"] , scenario_outline : & ["Struktura scenarija" , "Skica" , "Koncept"] , examples : & ["Primeri" , "Scenariji"] , given : & ["* " , "Za dato " , "Za date " , "Za dati "] , when : & ["* " , "Kada " , "Kad "] , then : & ["* " , "Onda "] , and : & ["* " , "I "] , but : & ["* " , "Ali "] , } ; const SV : Keywords < 'static > = Keywords { feature : & ["Egenskap"] , background : & ["Bakgrund"] , rule : & ["Regel"] , scenario : & ["Scenario"] , scenario_outline : & ["Abstrakt Scenario" , "Scenariomall"] , examples : & ["Exempel"] , given : & ["* " , "Givet "] , when : & ["* " , "När "] , then : & ["* " , "Så "] , and : & ["* " , "Och "] , but : & ["* " , "Men "] , } ; const TA : Keywords < 'static > = Keywords { feature : & ["அம\u{bcd}சம\u{bcd}" , "வணிக தேவை" , "திறன\u{bcd}"] , background : & ["பின\u{bcd}னணி"] , rule : & ["Rule"] , scenario : & ["உத\u{bbe}ரணம\u{bbe}க" , "க\u{bbe}ட\u{bcd}சி"] , scenario_outline : & ["க\u{bbe}ட\u{bcd}சி சுருக\u{bcd}கம\u{bcd}" , "க\u{bbe}ட\u{bcd}சி வ\u{bbe}ர\u{bcd}ப\u{bcd}புரு"] , examples : & ["எடுத\u{bcd}துக\u{bcd}க\u{bbe}ட\u{bcd}டுகள\u{bcd}" , "க\u{bbe}ட\u{bcd}சிகள\u{bcd}" , "நிலைமைகளில\u{bcd}"] , given : & ["* " , "கெ\u{bbe}டுக\u{bcd}கப\u{bcd}பட\u{bcd}ட "] , when : & ["* " , "எப\u{bcd}பே\u{bbe}து "] , then : & ["* " , "அப\u{bcd}பெ\u{bbe}ழுது "] , and : & ["* " , "மேலும\u{bcd}  " , "மற\u{bcd}றும\u{bcd} "] , but : & ["* " , "ஆன\u{bbe}ல\u{bcd}  "] , } ; const TE : Keywords < 'static > = Keywords { feature : & ["గుణము"] , background : & ["న\u{c47}పథ\u{c4d}యం"] , rule : & ["Rule"] , scenario : & ["ఉద\u{c3e}హరణ" , "సన\u{c4d}న\u{c3f}వ\u{c47}శం"] , scenario_outline : & ["కథనం"] , examples : & ["ఉద\u{c3e}హరణలు"] , given : & ["* " , "చ\u{c46}ప\u{c4d}పబడ\u{c3f}నద\u{c3f} "] , when : & ["* " , "ఈ పర\u{c3f}స\u{c4d}థ\u{c3f}త\u{c3f}ల\u{c4b} "] , then : & ["* " , "అప\u{c4d}పుడు "] , and : & ["* " , "మర\u{c3f}యు "] , but : & ["* " , "క\u{c3e}న\u{c3f} "] , } ; const TH : Keywords < 'static > = Keywords { feature : & ["โครงหล\u{e31}ก" , "ความต\u{e49}องการทางธ\u{e38}รก\u{e34}จ" , "ความสามารถ"] , background : & ["แนวค\u{e34}ด"] , rule : & ["Rule"] , scenario : & ["เหต\u{e38}การณ\u{e4c}"] , scenario_outline : & ["สร\u{e38}ปเหต\u{e38}การณ\u{e4c}" , "โครงสร\u{e49}างของเหต\u{e38}การณ\u{e4c}"] , examples : & ["ช\u{e38}ดของต\u{e31}วอย\u{e48}าง" , "ช\u{e38}ดของเหต\u{e38}การณ\u{e4c}"] , given : & ["* " , "กำหนดให\u{e49} "] , when : & ["* " , "เม\u{e37}\u{e48}อ "] , then : & ["* " , "ด\u{e31}งน\u{e31}\u{e49}น "] , and : & ["* " , "และ "] , but : & ["* " , "แต\u{e48} "] , } ; const TLH : Keywords < 'static > = Keywords { feature : & ["Qap" , "Qu'meH 'ut" , "perbogh" , "poQbogh malja'" , "laH"] , background : & ["mo'"] , rule : & ["Rule"] , scenario : & ["lut"] , scenario_outline : & ["lut chovnatlh"] , examples : & ["ghantoH" , "lutmey"] , given : & ["* " , "ghu' noblu' " , "DaH ghu' bejlu' "] , when : & ["* " , "qaSDI' "] , then : & ["* " , "vaj "] , and : & ["* " , "'ej " , "latlh "] , but : & ["* " , "'ach " , "'a "] , } ; const TR : Keywords < 'static > = Keywords { feature : & ["Özellik"] , background : & ["Geçmiş"] , rule : & ["Kural"] , scenario : & ["Örnek" , "Senaryo"] , scenario_outline : & ["Senaryo taslağı"] , examples : & ["Örnekler"] , given : & ["* " , "Diyelim ki "] , when : & ["* " , "Eğer ki "] , then : & ["* " , "O zaman "] , and : & ["* " , "Ve "] , but : & ["* " , "Fakat " , "Ama "] , } ; const TT : Keywords < 'static > = Keywords { feature : & ["Мөмкинлек" , "Үзенчәлеклелек"] , background : & ["Кереш"] , rule : & ["Rule"] , scenario : & ["Сценарий"] , scenario_outline : & ["Сценарийның төзелеше"] , examples : & ["Үрнәкләр" , "Мисаллар"] , given : & ["* " , "Әйтик "] , when : & ["* " , "Әгәр "] , then : & ["* " , "Нәтиҗәдә "] , and : & ["* " , "Һәм " , "Вә "] , but : & ["* " , "Ләкин " , "Әмма "] , } ; const UK : Keywords < 'static > = Keywords { feature : & ["Функціонал"] , background : & ["Передумова"] , rule : & ["Rule"] , scenario : & ["Приклад" , "Сценарій"] , scenario_outline : & ["Структура сценарію"] , examples : & ["Приклади"] , given : & ["* " , "Припустимо " , "Припустимо, що " , "Нехай " , "Дано "] , when : & ["* " , "Якщо " , "Коли "] , then : & ["* " , "То " , "Тоді "] , and : & ["* " , "І " , "А також " , "Та "] , but : & ["* " , "Але "] , } ; const UR : Keywords < 'static > = Keywords { feature : & ["صلاحیت" , "کاروبار کی ضرورت" , "خصوصیت"] , background : & ["پس منظر"] , rule : & ["Rule"] , scenario : & ["منظرنامہ"] , scenario_outline : & ["منظر نامے کا خاکہ"] , examples : & ["مثالیں"] , given : & ["* " , "اگر " , "بالفرض " , "فرض کیا "] , when : & ["* " , "جب "] , then : & ["* " , "پھر " , "تب "] , and : & ["* " , "اور "] , but : & ["* " , "لیکن "] , } ; const UZ : Keywords < 'static > = Keywords { feature : & ["Функционал"] , background : & ["Тарих"] , rule : & ["Rule"] , scenario : & ["Сценарий"] , scenario_outline : & ["Сценарий структураси"] , examples : & ["Мисоллар"] , given : & ["* " , "Агар "] , when : & ["* " , "Агар "] , then : & ["* " , "Унда "] , and : & ["* " , "Ва "] , but : & ["* " , "Лекин " , "Бирок " , "Аммо "] , } ; const VI : Keywords < 'static > = Keywords { feature : & ["Tính năng"] , background : & ["Bối cảnh"] , rule : & ["Rule"] , scenario : & ["Tình huống" , "Kịch bản"] , scenario_outline : & ["Khung tình huống" , "Khung kịch bản"] , examples : & ["Dữ liệu"] , given : & ["* " , "Biết " , "Cho "] , when : & ["* " , "Khi "] , then : & ["* " , "Thì "] , and : & ["* " , "Và "] , but : & ["* " , "Nhưng "] , } ; const ZH_CN : Keywords < 'static > = Keywords { feature : & ["功能"] , background : & ["背景"] , rule : & ["Rule"] , scenario : & ["场景" , "剧本"] , scenario_outline : & ["场景大纲" , "剧本大纲"] , examples : & ["例子"] , given : & ["* " , "假如" , "假设" , "假定"] , when : & ["* " , "当"] , then : & ["* " , "那么"] , and : & ["* " , "而且" , "并且" , "同时"] , but : & ["* " , "但是"] , } ; const ZH_TW : Keywords < 'static > = Keywords { feature : & ["功能"] , background : & ["背景"] , rule : & ["Rule"] , scenario : & ["場景" , "劇本"] , scenario_outline : & ["場景大綱" , "劇本大綱"] , examples : & ["例子"] , given : & ["* " , "假如" , "假設" , "假定"] , when : & ["* " , "當"] , then : & ["* " , "那麼"] , and : & ["* " , "而且" , "並且" , "同時"] , but : & ["* " , "但是"] , } ;