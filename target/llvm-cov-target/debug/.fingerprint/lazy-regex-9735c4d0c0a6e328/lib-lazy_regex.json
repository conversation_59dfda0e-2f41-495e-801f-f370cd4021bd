{"rustc": 4013192585442940105, "features": "[\"default\", \"regex\"]", "declared_features": "[\"default\", \"lite\", \"perf\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"regex\", \"regex-lite\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 6085252443868817606, "profile": 6609184196851301694, "path": 6654845857077833212, "deps": [[8244776183334334055, "once_cell", false, 7908477712405064288], [11529388793193449610, "lazy_regex_proc_macros", false, 2972844453360870278], [11641382387439738731, "regex", false, 14420606568225423369]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/lazy-regex-9735c4d0c0a6e328/dep-lib-lazy_regex"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 4506591960103940003, "config": 2202906307356721367, "compile_kind": 0}