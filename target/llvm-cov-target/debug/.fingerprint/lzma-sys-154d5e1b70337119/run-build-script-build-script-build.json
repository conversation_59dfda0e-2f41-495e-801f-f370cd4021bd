{"rustc": 4013192585442940105, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4230838335106841426, "build_script_build", false, 9283667440969872795]], "local": [{"RerunIfChanged": {"output": "debug/build/lzma-sys-154d5e1b70337119/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "LZMA_API_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_NO_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64_apple_darwin", "val": " -fprofile-instr-generate -fcoverage-mapping -fprofile-update=atomic"}}, {"RerunIfEnvChanged": {"var": "CC_SHELL_ESCAPED_FLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_aarch64-apple-darwin", "val": null}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 0, "config": 0, "compile_kind": 0}