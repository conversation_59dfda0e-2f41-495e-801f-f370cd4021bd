{"rustc": 4013192585442940105, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 6609184196851301694, "path": 1133772158177798004, "deps": [[554324495028472449, "memchr", false, 16271358923769264878], [6314779025451150414, "regex_automata", false, 7376559495426170016], [7325384046744447800, "aho_corasick", false, 1416141641186302400], [9111760993595911334, "regex_syntax", false, 6999923346918004338]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-ee393b09d74a7cf6/dep-lib-regex"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 0}