{"rustc": 4013192585442940105, "features": "[\"__tls\", \"blocking\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 3917452843314965808, "profile": 5177066816382677135, "path": 15217363415196784695, "deps": [[871868610555714878, "mime_guess", false, 11910987390329348924], [1668075563100350737, "native_tls_crate", false, 13959304300043656814], [2543237566893615891, "bytes", false, 6985814846671763372], [3476665202394793613, "tokio_native_tls", false, 12505410626602742975], [3930354675071354477, "percent_encoding", false, 2266840611054523333], [4804940174145429021, "tower", false, 10889752133695009785], [4890845594570020611, "sync_wrapper", false, 11558669564874810547], [5204382251033773414, "tower_service", false, 10108978826728564084], [5844868078238010658, "http_body", false, 4804069096086549721], [5846781562065118163, "futures_channel", false, 9240666684773805331], [6690072151007321369, "hyper", false, 17040142911729327444], [7009065068979121087, "http_body_util", false, 1548083017589625711], [7470442545028885647, "mime", false, 2258537519570124482], [8244776183334334055, "once_cell", false, 7908477712405064288], [9253677898334269643, "base64", false, 17153882596411958497], [9396302785578940539, "futures_core", false, 2049364681874293601], [9683973879422554196, "hyper_util", false, 2015941157197859082], [10188382419854199600, "system_configuration", false, 17742307256472287287], [10633404241517405153, "serde", false, 240114919729378959], [10654666947633555002, "rustls_pemfile", false, 254254392739608812], [10925028922895438183, "h2", false, 17224532039370838550], [11809678037142197677, "pin_project_lite", false, 6843537608454717085], [11852181446449194075, "hyper_tls", false, 15423418844757775636], [12226793623494822818, "encoding_rs", false, 10789810194739204691], [12368485582061518913, "http", false, 5671484276471889049], [12509852874546367857, "serde_json", false, 2185794768299717068], [15399619262696441677, "log", false, 17755431022015358707], [15501288286569156197, "serde_urlencoded", false, 5480560245681084612], [16433999612876168169, "ipnet", false, 8188775692239917452], [16476303074998891276, "futures_util", false, 16093907976918207247], [17333407209009474459, "tokio", false, 14835144150338457792], [18130989770956114225, "url", false, 11710044783394885379]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-3ea42d7fa69f8d77/dep-lib-reqwest"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 13557315201843503405, "config": 2202906307356721367, "compile_kind": 0}