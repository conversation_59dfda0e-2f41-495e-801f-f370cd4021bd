{"rustc": 4013192585442940105, "features": "[]", "declared_features": "[\"nightly\"]", "target": 12790597856430188652, "profile": 6609184196851301694, "path": 6774385215018420654, "deps": [[434554096238987306, "predicates_tree", false, 12560593060990457598], [2452538001284770427, "cfg_if", false, 17347545276097626239], [5742335004606619729, "downcast", false, 11531336763507506697], [9357895591565833906, "mockall_derive", false, 17150151812192345085], [10839265796338100058, "fragile", false, 12053033286807974875], [15625416449464819407, "predicates", false, 7325782862668004039]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mockall-0edefb30e5aacf1d/dep-lib-mockall"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 17800249630027319917, "config": 2202906307356721367, "compile_kind": 0}