{"rustc": 4013192585442940105, "features": "[\"default\", \"gzip\", \"std\", \"zlib\"]", "declared_features": "[\"default\", \"gzip\", \"nightly\", \"std\", \"zlib\"]", "target": 15239814365917440562, "profile": 6609184196851301694, "path": 14595733868435265942, "deps": [[414916092536737384, "<PERSON><PERSON>", false, 8326554187770157148], [416921746892697426, "crc32fast", false, 18424572671786209288], [15399619262696441677, "log", false, 17755431022015358707], [17229916178368048289, "simd_adler32", false, 7529850943138222354]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zopfli-3a5987d221460a2b/dep-lib-zopfli"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 2071715258961258256, "config": 2202906307356721367, "compile_kind": 0}