{"rustc": 4013192585442940105, "features": "[\"bundled_bindings\", \"default\", \"min_sqlite_version_3_14_0\", \"pkg-config\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"with-asan\"]", "target": 6899800862287646524, "profile": 6609184196851301694, "path": 13983709711154804623, "deps": [[8436338753864188372, "build_script_build", false, 4991737366034000107]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libsqlite3-sys-b02e6dae5ac665df/dep-lib-libsqlite3_sys"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 14430067081211154946, "config": 2202906307356721367, "compile_kind": 0}