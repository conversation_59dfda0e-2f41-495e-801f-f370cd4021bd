{"rustc": 4013192585442940105, "features": "[\"default\", \"serde\", \"std\"]", "declared_features": "[\"borsh\", \"c-repr\", \"db-diesel-mysql\", \"db-diesel-postgres\", \"db-diesel2-mysql\", \"db-diesel2-postgres\", \"db-postgres\", \"db-tokio-postgres\", \"default\", \"diesel\", \"legacy-ops\", \"macros\", \"maths\", \"maths-nopanic\", \"ndarray\", \"proptest\", \"rand\", \"rand-0_9\", \"rkyv\", \"rkyv-safe\", \"rocket-traits\", \"rust-fuzz\", \"serde\", \"serde-arbitrary-precision\", \"serde-bincode\", \"serde-float\", \"serde-str\", \"serde-with-arbitrary-precision\", \"serde-with-float\", \"serde-with-str\", \"serde_json\", \"std\", \"tokio-pg\", \"tokio-postgres\"]", "target": 9652763411108993936, "profile": 6707562999592697545, "path": 761662459955838885, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rust_decimal-d47a1cdf4bc68da3/dep-build-script-build-script-build"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 4850053393751688468, "config": 2202906307356721367, "compile_kind": 0}