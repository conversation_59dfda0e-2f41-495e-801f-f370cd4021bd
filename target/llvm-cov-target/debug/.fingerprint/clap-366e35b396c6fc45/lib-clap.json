{"rustc": 4013192585442940105, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\", \"wrap_help\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 12724100863246979317, "profile": 13557839117873329204, "path": 4689836544987181825, "deps": [[10784134162678770260, "clap_builder", false, 539375372440808939], [15995894937641404794, "clap_derive", false, 4028888314241940732]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-366e35b396c6fc45/dep-lib-clap"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 13636260659328210681, "config": 2202906307356721367, "compile_kind": 0}