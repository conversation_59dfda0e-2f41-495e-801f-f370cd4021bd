{"rustc": 4013192585442940105, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 367108867326171846, "profile": 15821721394985614323, "path": 8177197067259853406, "deps": [[461436706529125561, "futures_io", false, 977292735094938289], [1910231660504989506, "futures_task", false, 8211506753936885967], [5846781562065118163, "futures_channel", false, 9240666684773805331], [8083238378394459630, "futures_executor", false, 4297408333937002582], [9396302785578940539, "futures_core", false, 2049364681874293601], [11289432439818403777, "futures_sink", false, 12791082212748633397], [16476303074998891276, "futures_util", false, 16093907976918207247]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-620d77af4107f842/dep-lib-futures"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 7593721274762670645, "config": 2202906307356721367, "compile_kind": 0}