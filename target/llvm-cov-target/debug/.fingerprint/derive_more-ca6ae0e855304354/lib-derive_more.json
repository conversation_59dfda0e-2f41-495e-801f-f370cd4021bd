{"rustc": 4013192585442940105, "features": "[\"as_ref\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"into\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 5232885013608218659, "profile": 6707562999592697545, "path": 14968237944118177748, "deps": [[11852780150458150307, "syn", false, 9585557067743988201], [17525013869477438691, "quote", false, 15766748181239020803], [18036439996138669183, "proc_macro2", false, 3181049191361358098]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-ca6ae0e855304354/dep-lib-derive_more"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 8950704488499756937, "config": 2202906307356721367, "compile_kind": 0}