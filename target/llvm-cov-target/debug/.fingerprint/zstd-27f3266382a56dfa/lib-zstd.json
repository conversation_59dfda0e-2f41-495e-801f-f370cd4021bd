{"rustc": 4013192585442940105, "features": "[]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"thin\", \"thin-lto\", \"wasm\", \"zdict_builder\", \"zstdmt\"]", "target": 1685521507337952876, "profile": 6609184196851301694, "path": 13115103801626716349, "deps": [[7362043928874374465, "zstd_safe", false, 7581608263765609292]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zstd-27f3266382a56dfa/dep-lib-zstd"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 9485688690311560389, "config": 2202906307356721367, "compile_kind": 0}