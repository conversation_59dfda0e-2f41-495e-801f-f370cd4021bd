{"rustc": 4013192585442940105, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10213811073990997229, "build_script_build", false, 14198582342871657603]], "local": [{"RerunIfChanged": {"output": "debug/build/libm-b7f2dcf21eab6bd7/output", "paths": ["build.rs", "configure.rs"]}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 0, "config": 0, "compile_kind": 0}