{"rustc": 4013192585442940105, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 15187006570911712246, "profile": 18199194914698169809, "path": 3000898670535561974, "deps": [[3620218005424189895, "zstd_sys", false, 13060458885070034980], [7362043928874374465, "build_script_build", false, 2589741562910702282]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zstd-safe-b9a23231c2fadb69/dep-lib-zstd_safe"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 3120428256180033778, "config": 2202906307356721367, "compile_kind": 0}