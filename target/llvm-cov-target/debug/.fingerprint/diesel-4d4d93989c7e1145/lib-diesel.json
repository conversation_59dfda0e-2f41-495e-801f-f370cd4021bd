{"rustc": 4013192585442940105, "features": "[\"32-column-tables\", \"default\", \"r2d2\", \"sqlite\", \"with-deprecated\"]", "declared_features": "[\"128-column-tables\", \"32-column-tables\", \"64-column-tables\", \"__with_asan_tests\", \"chrono\", \"default\", \"extras\", \"huge-tables\", \"i-implement-a-third-party-backend-and-opt-into-breaking-changes\", \"ipnet-address\", \"large-tables\", \"mysql\", \"mysql_backend\", \"mysqlclient-src\", \"network-address\", \"numeric\", \"postgres\", \"postgres_backend\", \"pq-src\", \"quickcheck\", \"r2d2\", \"returning_clauses_for_sqlite_3_35\", \"serde_json\", \"sqlite\", \"time\", \"unstable\", \"uuid\", \"with-deprecated\", \"without-deprecated\"]", "target": 17231647715239326471, "profile": 6609184196851301694, "path": 15975217154099393222, "deps": [[7552645550685376377, "r2d2", false, 8314699118800864746], [8436338753864188372, "libsqlite3_sys", false, 14526780839878697761], [13316914120662226025, "diesel_derives", false, 5564713377312464]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/diesel-4d4d93989c7e1145/dep-lib-diesel"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 16324759234834716920, "config": 2202906307356721367, "compile_kind": 0}