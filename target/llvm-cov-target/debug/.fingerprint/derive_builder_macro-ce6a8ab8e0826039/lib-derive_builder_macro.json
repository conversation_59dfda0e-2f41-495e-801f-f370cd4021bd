{"rustc": 4013192585442940105, "features": "[\"lib_has_std\"]", "declared_features": "[\"alloc\", \"clippy\", \"lib_has_std\"]", "target": 45787181681555107, "profile": 6707562999592697545, "path": 11679306763372930747, "deps": [[2945863285247471626, "derive_builder_core", false, 5713538250315046868], [11852780150458150307, "syn", false, 9585557067743988201]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_builder_macro-ce6a8ab8e0826039/dep-lib-derive_builder_macro"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 15182123730245737964, "config": 2202906307356721367, "compile_kind": 0}