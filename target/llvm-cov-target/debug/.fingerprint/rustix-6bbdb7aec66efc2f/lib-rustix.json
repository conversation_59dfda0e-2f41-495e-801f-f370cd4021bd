{"rustc": 4013192585442940105, "features": "[\"alloc\", \"event\", \"fs\", \"libc-extra-traits\", \"net\", \"pipe\", \"process\", \"std\", \"time\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 1236213820636157529, "profile": 16415649242887926005, "path": 14500149172339255428, "deps": [[5912081054555210979, "libc_errno", false, 4963816148661130704], [7404036440948165371, "bitflags", false, 4417017909147225861], [7762067171913260472, "libc", false, 16119834969248363409], [14467156401149784211, "build_script_build", false, 407954110134321570]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-6bbdb7aec66efc2f/dep-lib-rustix"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 7953970670347159126, "config": 2202906307356721367, "compile_kind": 0}