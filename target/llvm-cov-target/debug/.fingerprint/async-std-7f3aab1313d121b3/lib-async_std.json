{"rustc": 4013192585442940105, "features": "[\"alloc\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"crossbeam-utils\", \"default\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"wasm-bindgen-futures\"]", "declared_features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"async-process\", \"attributes\", \"crossbeam-utils\", \"default\", \"docs\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"io_safety\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"surf\", \"tokio02\", \"tokio03\", \"tokio1\", \"unstable\", \"wasm-bindgen-futures\"]", "target": 18013193799273106157, "profile": 6609184196851301694, "path": 2970336815906161832, "deps": [[461436706529125561, "futures_io", false, 977292735094938289], [509169029968948458, "futures_lite", false, 13557805933395511047], [554324495028472449, "memchr", false, 9563422226991850251], [4761952582670444189, "pin_utils", false, 15180810049092039806], [8244776183334334055, "once_cell", false, 7908477712405064288], [8823464744775904706, "kv_log_macro", false, 6909651971319305032], [9396302785578940539, "futures_core", false, 2049364681874293601], [11597975563973993452, "async_io", false, 5348531062594191806], [11786503050965320974, "async_global_executor", false, 4010761685769432055], [11809678037142197677, "pin_project_lite", false, 6843537608454717085], [13100939403401765317, "crossbeam_utils", false, 497143492458155439], [13378362090485711775, "async_lock", false, 6262263466821755458], [15399619262696441677, "log", false, 17755431022015358707], [16861270911032234022, "async_channel", false, 4785095821500564177], [17040352472033410869, "slab", false, 1044909755292366201]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/async-std-7f3aab1313d121b3/dep-lib-async_std"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 148876777231142361, "config": 2202906307356721367, "compile_kind": 0}