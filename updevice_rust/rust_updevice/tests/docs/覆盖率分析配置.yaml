# Rust 项目覆盖率分析配置文件
# 用于存储不同项目的覆盖率分析参数

# UPDevice 项目配置
updevice:
  project_name: "rust_updevice"
  project_path: "updevice_rust/rust_updevice"
  test_type: "cucumber"
  source_filter: "updevice_rust/rust_updevice/src"
  output_file: "tests/docs/Updevice单测覆盖率分析报告.md"
  
  # 模块分类配置
  module_categories:
    - name: "核心模块覆盖率"
      modules:
        - "device_manager.rs"
        - "device_daemon.rs" 
        - "device_injection.rs"
        - "engine_device.rs"
        - "engine_device_extend.rs"
        - "device_core.rs"
    
    - name: "设备兼容性模块覆盖率"
      modules:
        - "voice_box_base.rs"
        - "voice_box_dot.rs"
        - "voice_box_dot2.rs"
        - "compat_device.rs"
    
    - name: "工厂模式模块覆盖率"
      modules:
        - "device_factory.rs"
        - "device_creator.rs"
    
    - name: "数据模型覆盖率"
      modules:
        - "device_basic.rs"
        - "device_permission.rs"
        - "device_relation.rs"
        - "device_info.rs"
        - "device_base_info.rs"
    
    - name: "守护进程模块覆盖率"
      modules:
        - "channel.rs"
        - "device_prepare.rs"
        - "extend_api_prepare.rs"
    
    - name: "数据源模块覆盖率"
      modules:
        - "device_list_data_source.rs"
        - "user_data_source.rs"
        - "config_data_source.rs"
    
    - name: "设备状态模块覆盖率"
      modules:
        - "device_connect_state.rs"
        - "device_online_state.rs"
        - "device_config_state.rs"
        - "device_sleep_state.rs"
        - "device_offline_cause.rs"
    
    - name: "工具类模块覆盖率"
      modules:
        - "fn_clone.rs"
        - "convert_vec.rs"

  # 覆盖率阈值配置
  thresholds:
    high: 90  # 高覆盖率阈值
    medium: 70  # 中等覆盖率阈值

# 通用配置
common:
  # 覆盖率命令模板
  commands:
    html_report: "cargo llvm-cov --test {test_type} --html"
    summary_report: "cargo llvm-cov --test {test_type} --summary-only"
    clean: "cargo llvm-cov clean"
    filter_data: "cargo llvm-cov --test {test_type} --summary-only | grep \"{source_filter}\""
  
  # 报告格式配置
  report_format:
    table_headers:
      - "模块"
      - "区域覆盖率"
      - "函数覆盖率"
      - "行覆盖率"
      - "说明"
    
    coverage_levels:
      - name: "高覆盖率模块"
        threshold: ">90%"
        color: "green"
      - name: "中等覆盖率模块"
        threshold: "70%-90%"
        color: "yellow"
      - name: "需要改进的模块"
        threshold: "<70%"
        color: "red"
  
  # 质量检查配置
  quality_checks:
    - "数据准确性检查"
    - "报告完整性检查"
    - "格式规范性检查"
    - "建议可行性检查"

# 版本信息
version: "1.0"
last_updated: "2024-12"
maintainer: "AI Assistant"