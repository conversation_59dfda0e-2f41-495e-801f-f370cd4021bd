use std::sync::Arc;

use cucumber::gherkin::Step;
use cucumber::{given, when};

use crate::fake::fake_config_data_source::FakeConfigDataSource;
use crate::fake::fake_device::FakeDevice;
use crate::fake::fake_device_factory::FakeDeviceFactory;
use crate::utils::device_test_holder::UpDeviceTestHolder;
use crate::utils::step_utils::get_step_utils;
use crate::MyWorld;
use rust_updevice::api::device_injection::UpDeviceInjection;
use rust_updevice::device::aggregate_device::UpAggregateDevice;
use rust_updevice::device::common_device::UpCommonDevice;
use rust_updevice::device::engine_device::UpEngineDevice;
use rust_updevice::device::washing_device::UpWashingDevice;
use rust_updevice::factory::device_factory::DeviceFactory;
use rust_updevice::models::device_toolkit::MockUpDeviceToolkit;

#[given(expr = "使用者创建{string}设备工厂")]
fn given_user_create_device_factory(_world: &mut MyWorld, device_factory_type: String) {
    if device_factory_type == "测试" {
        let device_factory = Box::new(FakeDeviceFactory::new());
        UpDeviceTestHolder::get_instance().set_device_factory(device_factory);
    } else if device_factory_type == "逻辑引擎" {
        UpDeviceTestHolder::get_instance().set_real_device_factory(DeviceFactory::new());
    }
}

#[given(expr = "创建{string}设备,唯一标识ID为{string},设备信息如下:")]
fn given_create_device(_world: &mut MyWorld, device_type: String, unique_id: String, step: &Step) {
    let device_info_list = get_step_utils().convert_device_info_from_step(step);
    let first_device = device_info_list.first().unwrap().clone();
    match device_type.as_str() {
        "测试" => {
            let fake_device = FakeDevice::new(unique_id.clone(), first_device);
            UpDeviceTestHolder::get_instance().set_fake_device(fake_device);
        }
        "逻辑引擎" => {
            let engine_device = UpEngineDevice::new(unique_id.as_str(), first_device);
            UpDeviceTestHolder::get_instance().set_engine_device(engine_device);
        }
        "聚合" => {
            let aggregate_device = UpAggregateDevice::new(unique_id.clone(), first_device);
            UpDeviceTestHolder::get_instance()
                .set_test_device(&unique_id, Arc::new(aggregate_device));
        }
        "通用" => {
            let common_device = UpCommonDevice::new(&unique_id, first_device);
            UpDeviceTestHolder::get_instance().set_test_device(&unique_id, Arc::new(common_device));
        }
        "洗衣机" => {
            let washing_device = UpWashingDevice::new(unique_id.clone(), first_device);
            UpDeviceTestHolder::get_instance()
                .set_test_device(&unique_id, Arc::new(washing_device));
        }
        _ => {}
    }
}

#[when(expr = "等待{string}秒")]
fn when_wait_seconds(_world: &mut MyWorld, seconds: String) {
    std::thread::sleep(std::time::Duration::from_secs(
        seconds.parse::<u64>().unwrap(),
    ));
}

#[given(expr = "Toolkit使用{string}")]
fn toolkit_use_toolkit(_world: &mut MyWorld, toolkit_type: String) {
    if toolkit_type == "模拟的" {
        let mock_toolkit = MockUpDeviceToolkit::default();
        UpDeviceInjection::get_instance().set_device_toolkit(Box::new(mock_toolkit));
    }
}
#[given(expr = "资源管理器使用{string}")]
fn set_resource_manager(_world: &mut MyWorld, resource_type: String) {
    if resource_type == "模拟的" {
        UpDeviceInjection::get_instance()
            .set_config_data_source(Arc::new(FakeConfigDataSource::new()));
    }
}
