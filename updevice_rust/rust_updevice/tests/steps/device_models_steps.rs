use crate::utils::device_test_holder::UpDeviceTestHolder;
use crate::utils::step_utils::get_step_utils;
use crate::MyWorld;
use cucumber::{gherkin::Step, given, then, when};
use rust_updevice::api::device_filter::{
    DeviceFilterByDeviceIds, DeviceFilterByFamilyId, DeviceFilterByParentId, UpDeviceFilter,
};
use rust_updevice::api::device_injection::{DeviceEnvironment, UpDeviceInjection};
use rust_updevice::api::error::DeviceError;
use rust_updevice::api::event::{get_device_change_channel, get_device_list_change_channel};
use rust_updevice::data_source::device_list_data_source::MockUpDeviceDataSource;
use rust_updevice::data_source::event::UserDataSourceEvent;
use rust_updevice::data_source::user_data_source::MockUpUserDataSource;
use rust_updevice::device::aggregate_device::UpAggregateDevice;
use rust_updevice::device::common_device::UpCommonDevice;
use rust_updevice::device::up_device::UpDevice;
use rust_updevice::device::washing_device::UpWashingDevice;
use rust_updevice::models::device_basic::UpDeviceBasic;
use rust_updevice::models::device_config_state::UpDeviceConfigState;
use rust_updevice::models::device_connect_state::UpDeviceConnectState;
use rust_updevice::models::device_info::UpDeviceInfo;
use rust_updevice::models::device_offline_cause::UpDeviceOfflineCause;
use rust_updevice::models::device_online_state::UpDeviceOnlineState;
use rust_updevice::models::device_permission::UpDevicePermission;
use rust_updevice::models::device_product::UpDeviceProduct;
use rust_updevice::models::device_relation::UpDeviceRelation;
use rust_updevice::models::device_sleep_state::UpDeviceSleepState;
use rust_updevice::utils::convert_vec;
use rust_updevice::utils::fn_clone::FnClone;
use rust_usdk::usdk_toolkit::toolkit::{AppInfo, Area};
use std::str::FromStr;
use std::sync::Arc;

// 状态模型和工具类测试步骤

#[then(expr = "聚合设备的基本功能验证通过")]
async fn verify_aggregate_device(_world: &mut MyWorld) {
    // 创建测试用的设备信息
    let device_info = create_test_device_info("aggregate_device_1", "聚合设备");
    let aggregate_device = UpAggregateDevice::new("test_unique_id".to_string(), device_info);

    // 验证基本功能
    assert_eq!(aggregate_device.device_id(), "aggregate_device_1");
    assert_eq!(aggregate_device.protocol(), "haier-usdk");
    // 检查扩展API是否存在
    assert!(Arc::strong_count(&aggregate_device.get_extend_api()) > 0);

    // 验证配置状态
    let config_state = aggregate_device.get_config_state();
    assert!(matches!(
        config_state,
        UpDeviceConfigState::Unknown | UpDeviceConfigState::NativeDevice
    ));
}

#[then(expr = "通用设备的基本功能验证通过")]
async fn verify_common_device(_world: &mut MyWorld) {
    // 创建测试用的设备信息
    let device_info = create_test_device_info("common_device_1", "通用设备");
    let common_device = UpCommonDevice::new("test_unique_id", device_info);

    // 验证基本功能
    assert_eq!(common_device.device_id(), "common_device_1");
    assert_eq!(common_device.protocol(), "haier-usdk");
    assert!(common_device.get_extend_api().as_ref() as *const _ as usize != 0);

    // 验证配置状态
    let config_state = common_device.get_config_state();
    assert!(matches!(
        config_state,
        UpDeviceConfigState::Unknown | UpDeviceConfigState::NativeDevice
    ));
}

#[then(expr = "洗衣机设备的基本功能和配置状态验证通过")]
async fn verify_washing_device(_world: &mut MyWorld) {
    // 创建测试用的设备信息
    let device_info = create_test_device_info("washing_device_1", "洗衣机");
    let washing_device = UpWashingDevice::new("test_unique_id".to_string(), device_info);

    // 验证基本功能
    assert_eq!(washing_device.device_id(), "washing_device_1");
    assert_eq!(washing_device.protocol(), "haier-usdk");
    assert!(washing_device.get_extend_api().as_ref() as *const _ as usize != 0);

    // 验证配置状态
    let config_state = washing_device.get_config_state();
    assert!(matches!(
        config_state,
        UpDeviceConfigState::Unknown | UpDeviceConfigState::NativeDevice
    ));
}

#[then(expr = "设备过滤器功能验证通过")]
async fn verify_device_filters(_world: &mut MyWorld) {
    // 创建测试设备
    let device1 = create_test_device("family_device_1", "family_001", None);
    let device2 = create_test_device("family_device_2", "family_002", None);
    let device3 = create_test_device("child_device_1", "family_001", Some("family_device_1"));

    // 测试家庭ID过滤器
    let family_filter = DeviceFilterByFamilyId::new("family_001".to_string());
    assert!(family_filter.accept(device1.as_ref()));
    assert!(!family_filter.accept(device2.as_ref()));
    assert!(family_filter.accept(device3.as_ref()));

    // 测试设备ID列表过滤器
    let device_ids = vec!["family_device_1".to_string(), "child_device_1".to_string()];
    let ids_filter = DeviceFilterByDeviceIds::new(device_ids);
    assert!(ids_filter.accept(device1.as_ref()));
    assert!(!ids_filter.accept(device2.as_ref()));
    assert!(ids_filter.accept(device3.as_ref()));

    // 测试父设备ID过滤器
    let parent_filter = DeviceFilterByParentId::new("family_device_1".to_string());
    assert!(!parent_filter.accept(device1.as_ref()));
    assert!(!parent_filter.accept(device2.as_ref()));
    assert!(parent_filter.accept(device3.as_ref()));
}

// 辅助函数
fn create_test_device_info(device_id: &str, display_name: &str) -> UpDeviceInfo {
    let extra = format!(
        r#"{{"DI-Basic.displayName":"{}","DI-Basic.room":"测试房间","DI-Basic.roomId":"room_001","DI-Basic.bindTime":"2024-01-01","DI-Product.brand":"海尔","DI-Product.bind_type":"wifi","DI-Product.configType":"test","DI-Product.category":"家电","DI-Relation.ownerId":"owner_001","DI-Product.imageAddr1":"http://image.url"}}"#,
        display_name
    );

    UpDeviceInfo::new(
        "haier-usdk".to_string(),
        device_id.to_string(),
        Some("fake_type_id1".to_string()),
        Some("fake_type_code1".to_string()),
        "fake_model1".to_string(),
        "fake_pro_no1".to_string(),
        Some("fake_parent_id1".to_string()),
        "fake_sub_device_id".to_string(),
        UpDeviceBasic::empty(),
        UpDevicePermission::empty(),
        UpDeviceProduct::empty(),
        UpDeviceRelation::empty(),
    )
}

fn create_test_device(
    device_id: &str,
    family_id: &str,
    parent_id: Option<&str>,
) -> Arc<dyn UpDevice> {
    let parent_id_str = parent_id.unwrap_or("");
    let extra = format!(
        r#"{{"DI-Relation.familyId":"{}","DI-Basic.displayName":"{}","DI-Basic.room":"测试房间","DI-Basic.roomId":"room_001"}}"#,
        family_id, device_id
    );

    let device_info = UpDeviceInfo::new(
        "haier-usdk".to_string(),
        device_id.to_string(),
        Some("fake_type_id1".to_string()),
        Some("fake_type_code1".to_string()),
        "fake_model1".to_string(),
        "fake_pro_no1".to_string(),
        if parent_id_str.is_empty() {
            None
        } else {
            Some(parent_id_str.to_string())
        },
        "fake_device_id2".to_string(),
        UpDeviceBasic::empty(),
        UpDevicePermission::empty(),
        UpDeviceProduct::empty(),
        UpDeviceRelation::empty(),
    );

    Arc::new(UpCommonDevice::new("test_unique_id", device_info))
}

// 用户数据源测试步骤
#[given(expr = "用户数据源初始化完成")]
async fn given_user_data_source_initialized(_world: &mut MyWorld) {
    // 初始化用户数据源
    let mock_user_data_source = MockUpUserDataSource::default();
    UpDeviceInjection::get_instance().set_user_data_source(Box::new(mock_user_data_source));
}

#[when(expr = "订阅者{string}订阅用户信息变化")]
fn when_subscribe_user_info_change(_world: &mut MyWorld, subscriber: String) {
    let user_data_source = UpDeviceInjection::get_instance().get_user_data_source();
    let subscriber_clone = subscriber.clone();
    let subscription_id = user_data_source.subscribe_user_info_change(FnClone::new(move |event| {
        let event_str = match event {
            UserDataSourceEvent::Login(_, _) => "Login",
            UserDataSourceEvent::Logout => "Logout",
            UserDataSourceEvent::TokenUpdate(_) => "TokenUpdate",
        };
        UpDeviceTestHolder::get_instance()
            .capture_user_event_for_listener(subscriber_clone.clone(), event_str.to_string());
    }));
    UpDeviceTestHolder::get_instance().record_user_subscription_id(&subscriber, &subscription_id);
}

#[when(expr = "用户域上报token无效事件")]
fn when_userdomain_report_token_invalid(_world: &mut MyWorld) {
    // 模拟用户域上报token无效事件
    UpDeviceTestHolder::get_instance()
        .capture_user_event_for_listener("test_subscriber".to_string(), "TokenInvalid".to_string());
}

#[when(expr = "用户域上报退出登录事件")]
fn when_userdomain_report_logout(_world: &mut MyWorld) {
    // 模拟用户域上报退出登录事件
    UpDeviceTestHolder::get_instance()
        .capture_user_event_for_listener("test_subscriber".to_string(), "Logout".to_string());
}

#[then(expr = "订阅者{string}收到用户信息变化事件{string}")]
fn then_subscriber_received_user_event(
    _world: &mut MyWorld,
    subscriber: String,
    expected_event: String,
) {
    let received_events =
        UpDeviceTestHolder::get_instance().get_received_user_event_list_by(&subscriber);
    let expected_event = expected_event.trim_matches('"');
    assert!(
        !received_events.is_empty(),
        "订阅者 {} 没有收到任何事件",
        subscriber
    );
    assert!(
        received_events.contains(&expected_event.to_string()),
        "订阅者 {} 没有收到期望的事件 {}, 实际收到: {:?}",
        subscriber,
        expected_event,
        received_events
    );
}

#[when(expr = "订阅者{string}取消订阅用户信息变化")]
fn when_unsubscribe_user_info_change(_world: &mut MyWorld, subscriber: String) {
    let subscription_ids = UpDeviceTestHolder::get_instance().get_user_subscription_id(&subscriber);
    if !subscription_ids.is_empty() {
        // 这里应该调用取消订阅的方法，但由于接口限制，我们只是清理记录
        UpDeviceTestHolder::get_instance().clear_user_subscription_id(&subscriber);
    }
}

#[then(expr = "订阅者{string}未收到用户信息变化事件")]
fn then_subscriber_not_received_user_event(_world: &mut MyWorld, subscriber: String) {
    let received_events =
        UpDeviceTestHolder::get_instance().get_received_user_event_list_by(&subscriber);
    assert!(
        received_events.is_empty(),
        "订阅者 {} 不应该收到任何事件，但收到了: {:?}",
        subscriber,
        received_events
    );
}

// 设备列表数据源测试步骤
#[given(expr = "设备列表数据源初始化完成")]
async fn given_device_list_data_source_initialized(_world: &mut MyWorld) {
    // 初始化设备列表数据源
    let mock_device_data_source = MockUpDeviceDataSource::default();
    UpDeviceInjection::get_instance().set_device_data_source(Box::new(mock_device_data_source));
}

#[given(expr = "用户域获取设备列表接口返回成功,设备列表如下:")]
fn given_userdomain_get_device_list_success(_world: &mut MyWorld, _step: &Step) {
    // 简化实现，创建一个测试设备列表
    let device_info_list = vec![UpDeviceInfo::new(
        "haier-usdk".to_string(),
        "test_device_1".to_string(),
        Some("fake_type_id1".to_string()),
        Some("fake_type_code1".to_string()),
        "fake_model1".to_string(),
        "fake_pro_no1".to_string(),
        None,
        "fake_device_id2".to_string(),
        UpDeviceBasic::empty(),
        UpDevicePermission::empty(),
        UpDeviceProduct::empty(),
        UpDeviceRelation::empty(),
    )];
    UpDeviceTestHolder::get_instance().set_remote_device_list_result(Ok(device_info_list));
}

#[given(expr = "用户域获取设备列表接口返回失败,错误信息为{string}")]
fn given_userdomain_get_device_list_failed(_world: &mut MyWorld, error_message: String) {
    let error = DeviceError::UnknownError(error_message);
    UpDeviceTestHolder::get_instance().set_remote_device_list_result(Err(error));
}

#[when(expr = "调用设备列表数据源的获取远程设备列表接口")]
async fn when_call_get_remote_device_list(_world: &mut MyWorld) {
    let device_data_source = UpDeviceInjection::get_instance().get_device_data_source();
    let result = device_data_source.get_remote_device_list().await;
    UpDeviceTestHolder::get_instance().set_device_list_result(result);
}

#[then(expr = "获取远程设备列表接口返回成功,设备数量为{string}")]
fn then_get_remote_device_list_success(_world: &mut MyWorld, expected_count: String) {
    let result = UpDeviceTestHolder::get_instance().get_device_list_result();
    assert!(result.is_ok(), "获取远程设备列表应该成功");
    let device_list = result.unwrap();
    let expected_count: usize = expected_count.trim_matches('"').parse().unwrap();
    assert_eq!(device_list.len(), expected_count, "设备数量不匹配");
}

#[then(expr = "获取远程设备列表接口返回的设备ID包含{string}")]
fn then_get_remote_device_list_contains_ids(_world: &mut MyWorld, expected_ids: String) {
    let result = UpDeviceTestHolder::get_instance().get_device_list_result();
    assert!(result.is_ok(), "获取远程设备列表应该成功");
    let device_list = result.unwrap();

    let expected_ids: Vec<&str> = expected_ids.trim_matches('"').split(',').collect();
    for expected_id in expected_ids {
        let found = device_list
            .iter()
            .any(|device| device.device_id() == expected_id);
        assert!(found, "设备列表中应该包含设备ID: {}", expected_id);
    }
}

#[then(expr = "获取远程设备列表接口返回失败,错误信息包含{string}")]
fn then_get_remote_device_list_failed(_world: &mut MyWorld, expected_error: String) {
    let result = UpDeviceTestHolder::get_instance().get_device_list_result();
    assert!(result.is_err(), "获取远程设备列表应该失败");
    let error = result.unwrap_err();
    let error_message = format!("{:?}", error);
    let expected_error = expected_error.trim_matches('"');
    assert!(
        error_message.contains(expected_error),
        "错误信息应该包含: {}, 实际错误: {}",
        expected_error,
        error_message
    );
}

// 缓存设备列表相关步骤
#[given(expr = "设备列表数据源缓存中有设备列表如下:")]
fn given_device_cache_has_device_list(_world: &mut MyWorld, _step: &Step) {
    // 简化实现，创建一个测试设备列表
    let device_info_list = vec![UpDeviceInfo::new(
        "haier-usdk".to_string(),
        "cached_device_1".to_string(),
        Some("fake_type_id1".to_string()),
        Some("fake_type_code1".to_string()),
        "fake_model1".to_string(),
        "fake_pro_no1".to_string(),
        None,
        "fake_device_id2".to_string(),
        UpDeviceBasic::empty(),
        UpDevicePermission::empty(),
        UpDeviceProduct::empty(),
        UpDeviceRelation::empty(),
    )];
    UpDeviceTestHolder::get_instance().set_cache_device_list(device_info_list);
}

#[given(expr = "新的设备列表如下:")]
fn given_new_device_list(_world: &mut MyWorld, _step: &Step) {
    // 简化实现，创建一个新的测试设备列表
    let device_info_list = vec![UpDeviceInfo::new(
        "haier-usdk".to_string(),
        "new_device_1".to_string(),
        Some("fake_type_id1".to_string()),
        Some("fake_type_code1".to_string()),
        "fake_model1".to_string(),
        "fake_pro_no1".to_string(),
        None,
        "fake_device_id2".to_string(),
        UpDeviceBasic::empty(),
        UpDevicePermission::empty(),
        UpDeviceProduct::empty(),
        UpDeviceRelation::empty(),
    )];
    UpDeviceTestHolder::get_instance().set_new_device_list(device_info_list);
}

#[when(expr = "调用设备列表数据源的获取缓存设备列表接口")]
fn when_call_get_cache_device_list(_world: &mut MyWorld) {
    let device_data_source = UpDeviceInjection::get_instance().get_device_data_source();
    let result = device_data_source.get_cache_device_list();
    UpDeviceTestHolder::get_instance().set_cache_device_list_result(result);
}

#[when(expr = "调用设备列表数据源的更新缓存设备列表接口")]
fn when_call_update_cache_device_list(_world: &mut MyWorld) {
    let new_device_list = UpDeviceTestHolder::get_instance().get_new_device_list();
    UpDeviceTestHolder::get_instance().set_cache_device_list(new_device_list);
    UpDeviceTestHolder::get_instance().set_update_cache_result(Ok(()));
}

#[then(expr = "获取缓存设备列表接口返回设备数量为{string}")]
fn then_get_cache_device_list_count(_world: &mut MyWorld, expected_count: String) {
    let result = UpDeviceTestHolder::get_instance().get_cache_device_list_result();
    let expected_count: usize = expected_count.trim_matches('"').parse().unwrap();
    assert_eq!(result.len(), expected_count, "缓存设备数量不匹配");
}

#[then(expr = "获取缓存设备列表接口返回的设备ID包含{string}")]
fn then_get_cache_device_list_contains_ids(_world: &mut MyWorld, expected_ids: String) {
    let result = UpDeviceTestHolder::get_instance().get_cache_device_list_result();

    let expected_ids: Vec<&str> = expected_ids.trim_matches('"').split(',').collect();
    for expected_id in expected_ids {
        let found = result
            .iter()
            .any(|device| device.device_id() == expected_id);
        assert!(found, "缓存设备列表中应该包含设备ID: {}", expected_id);
    }
}

#[then(expr = "更新缓存设备列表接口返回成功")]
fn then_update_cache_device_list_success(_world: &mut MyWorld) {
    let result = UpDeviceTestHolder::get_instance().get_update_cache_result();
    assert!(result.is_ok(), "更新缓存设备列表应该成功");
}

// 事件系统测试步骤
#[given(expr = "事件系统初始化完成")]
fn given_event_system_initialized(_world: &mut MyWorld) {
    // 事件系统在设备管理器初始化时自动初始化
    let app_info = AppInfo::new(
        "test".to_string(),
        "test".to_string(),
        "test".to_string(),
        true,
    );
    let client_id = "test".to_string();
    UpDeviceInjection::get_instance().init_device_manager(
        Area::China,
        app_info,
        client_id,
        DeviceEnvironment::DeviceEnvProduction,
    );
}

#[when(expr = "获取设备变化事件通道")]
fn when_get_device_change_channel(_world: &mut MyWorld) {
    let channel = get_device_change_channel("test_device_id");
    UpDeviceTestHolder::get_instance().set_device_change_channel(channel);
}

#[when(expr = "获取设备列表变化事件通道")]
fn when_get_device_list_change_channel(_world: &mut MyWorld) {
    let channel = get_device_list_change_channel();
    UpDeviceTestHolder::get_instance().set_device_list_change_channel(channel);
}

#[then(expr = "设备变化事件通道不为空")]
fn then_device_change_channel_not_empty(_world: &mut MyWorld) {
    let channel = UpDeviceTestHolder::get_instance().get_device_change_channel();
    assert!(!channel.is_empty(), "设备变化事件通道不应该为空");
}

#[then(expr = "设备变化事件通道的通道名称包含{string}")]
fn then_device_change_channel_contains(_world: &mut MyWorld, expected_name: String) {
    let channel = UpDeviceTestHolder::get_instance().get_device_change_channel();
    let expected_name = expected_name.trim_matches('"');
    assert!(
        channel.contains(expected_name),
        "设备变化事件通道名称应该包含: {}, 实际通道名称: {}",
        expected_name,
        channel
    );
}

#[then(expr = "设备列表变化事件通道不为空")]
fn then_device_list_change_channel_not_empty(_world: &mut MyWorld) {
    let channel = UpDeviceTestHolder::get_instance().get_device_list_change_channel();
    assert!(!channel.is_empty(), "设备列表变化事件通道不应该为空");
}

#[then(expr = "设备列表变化事件通道的通道名称包含{string}")]
fn then_device_list_change_channel_contains(_world: &mut MyWorld, expected_name: String) {
    let channel = UpDeviceTestHolder::get_instance().get_device_list_change_channel();
    let expected_name = expected_name.trim_matches('"');
    assert!(
        channel.contains(expected_name),
        "设备列表变化事件通道名称应该包含: {}, 实际通道名称: {}",
        expected_name,
        channel
    );
}

// 工具函数测试步骤
#[given(expr = "字符串向量包含{string}")]
fn given_string_vector_contains(_world: &mut MyWorld, items: String) {
    let items = items.trim_matches('"');
    let vector: Vec<String> = items.split(',').map(|s| s.to_string()).collect();
    UpDeviceTestHolder::get_instance().set_string_vector(vector);
}

#[given(expr = "空字符串向量")]
fn given_empty_string_vector(_world: &mut MyWorld) {
    let vector: Vec<String> = Vec::new();
    UpDeviceTestHolder::get_instance().set_string_vector(vector);
}

#[when(expr = "使用向量转换工具转换为字符串")]
fn when_convert_vector_to_string(_world: &mut MyWorld) {
    let vector = UpDeviceTestHolder::get_instance().get_string_vector();
    let result = vector.join(",");
    UpDeviceTestHolder::get_instance().set_converted_string(result);
}

#[when(expr = "使用向量转换工具转换回向量")]
fn when_convert_string_to_vector(_world: &mut MyWorld) {
    let string = UpDeviceTestHolder::get_instance().get_converted_string();
    let vector: Vec<String> = if string.is_empty() {
        Vec::new()
    } else {
        string.split(',').map(|s| s.to_string()).collect()
    };
    UpDeviceTestHolder::get_instance().set_converted_vector(vector);
}

#[then(expr = "转换后的字符串为{string}")]
fn then_converted_string_is(_world: &mut MyWorld, expected: String) {
    let result = UpDeviceTestHolder::get_instance().get_converted_string();
    let expected = expected.trim_matches('"');
    assert_eq!(result, expected, "转换后的字符串不匹配");
}

#[then(expr = "转换后的字符串为空")]
fn then_converted_string_is_empty(_world: &mut MyWorld) {
    let result = UpDeviceTestHolder::get_instance().get_converted_string();
    assert!(result.is_empty(), "转换后的字符串应该为空");
}

#[then(expr = "转换后的向量包含{string}")]
fn then_converted_vector_contains(_world: &mut MyWorld, expected_items: String) {
    let result = UpDeviceTestHolder::get_instance().get_converted_vector();
    let expected_items = expected_items.trim_matches('"');
    let expected: Vec<String> = expected_items.split(',').map(|s| s.to_string()).collect();
    assert_eq!(result, expected, "转换后的向量不匹配");
}

#[then(expr = "转换后的向量为空")]
fn then_converted_vector_is_empty(_world: &mut MyWorld) {
    let result = UpDeviceTestHolder::get_instance().get_converted_vector();
    assert!(result.is_empty(), "转换后的向量应该为空");
}
