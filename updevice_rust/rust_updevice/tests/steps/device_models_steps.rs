use crate::MyWorld;
use cucumber::then;
use rust_updevice::api::device_filter::{
    DeviceFilterByDeviceIds, DeviceFilterByFamilyId, DeviceFilterByParentId, UpDeviceFilter,
};
use rust_updevice::device::aggregate_device::UpAggregateDevice;
use rust_updevice::device::common_device::UpCommonDevice;
use rust_updevice::device::up_device::UpDevice;
use rust_updevice::device::washing_device::UpWashingDevice;
use rust_updevice::models::device_config_state::UpDeviceConfigState;
use rust_updevice::models::device_connect_state::UpDeviceConnectState;
use rust_updevice::models::device_info::UpDeviceInfo;
use rust_updevice::models::device_offline_cause::UpDeviceOfflineCause;
use rust_updevice::models::device_online_state::UpDeviceOnlineState;
use rust_updevice::models::device_sleep_state::UpDeviceSleepState;
use rust_updevice::utils::convert_vec;
use std::str::FromStr;
use std::sync::Arc;

// 状态模型和工具类测试步骤

#[then(regex = r"^UpDeviceConfigState的Display和FromStr功能验证通过$")]
async fn verify_device_config_state(_world: &mut MyWorld) {
    // 测试 Display 功能
    assert_eq!(UpDeviceConfigState::Unknown.to_string(), "Unknown");
    assert_eq!(UpDeviceConfigState::NotSupport.to_string(), "NotSupport");
    assert_eq!(UpDeviceConfigState::Support.to_string(), "Support");
    assert_eq!(
        UpDeviceConfigState::NativeDevice.to_string(),
        "NativeDevice"
    );
    assert_eq!(
        UpDeviceConfigState::NotNetworkDevice.to_string(),
        "NotNetworkDevice"
    );

    // 测试 FromStr 功能
    assert_eq!(
        UpDeviceConfigState::from_str("UNKNOWN").unwrap(),
        UpDeviceConfigState::Unknown
    );
    assert_eq!(
        UpDeviceConfigState::from_str("NOT_SUPPORT").unwrap(),
        UpDeviceConfigState::NotSupport
    );
    assert_eq!(
        UpDeviceConfigState::from_str("SUPPORT").unwrap(),
        UpDeviceConfigState::Support
    );
    assert_eq!(
        UpDeviceConfigState::from_str("NATIVE_DEVICE").unwrap(),
        UpDeviceConfigState::NativeDevice
    );
    assert_eq!(
        UpDeviceConfigState::from_str("NOT_NETWORK").unwrap(),
        UpDeviceConfigState::NotNetworkDevice
    );
    assert!(UpDeviceConfigState::from_str("INVALID").is_err());
}

#[then(regex = r"^UpDeviceConnectState的Display和FromStr功能验证通过$")]
async fn verify_device_connect_state(_world: &mut MyWorld) {
    // 测试 Display 功能
    assert_eq!(UpDeviceConnectState::Released.to_string(), "Released");
    assert_eq!(UpDeviceConnectState::Preparing.to_string(), "Preparing");
    assert_eq!(UpDeviceConnectState::Prepared.to_string(), "Prepared");
    assert_eq!(UpDeviceConnectState::Connecting.to_string(), "Connecting");
    assert_eq!(UpDeviceConnectState::Ready.to_string(), "Ready");
    assert_eq!(UpDeviceConnectState::Releasing.to_string(), "Releasing");

    // 测试 FromStr 功能
    assert_eq!(
        UpDeviceConnectState::from_str("RELEASED").unwrap(),
        UpDeviceConnectState::Released
    );
    assert_eq!(
        UpDeviceConnectState::from_str("PREPARING").unwrap(),
        UpDeviceConnectState::Preparing
    );
    assert_eq!(
        UpDeviceConnectState::from_str("PREPARED").unwrap(),
        UpDeviceConnectState::Prepared
    );
    assert_eq!(
        UpDeviceConnectState::from_str("CONNECTING").unwrap(),
        UpDeviceConnectState::Connecting
    );
    assert_eq!(
        UpDeviceConnectState::from_str("READY").unwrap(),
        UpDeviceConnectState::Ready
    );
    assert_eq!(
        UpDeviceConnectState::from_str("RELEASING").unwrap(),
        UpDeviceConnectState::Releasing
    );
    assert!(UpDeviceConnectState::from_str("INVALID").is_err());
}

#[then(regex = r"^UpDeviceOnlineState的Display和FromStr功能验证通过$")]
async fn verify_device_online_state(_world: &mut MyWorld) {
    // 测试 Display 功能
    assert_eq!(UpDeviceOnlineState::Unknown.to_string(), "Unknown");
    assert_eq!(UpDeviceOnlineState::Online.to_string(), "Online");
    assert_eq!(UpDeviceOnlineState::Offline.to_string(), "Offline");

    // 测试 FromStr 功能
    assert_eq!(
        UpDeviceOnlineState::from_str("UNKNOWN").unwrap(),
        UpDeviceOnlineState::Unknown
    );
    assert_eq!(
        UpDeviceOnlineState::from_str("ONLINE").unwrap(),
        UpDeviceOnlineState::Online
    );
    assert_eq!(
        UpDeviceOnlineState::from_str("OFFLINE").unwrap(),
        UpDeviceOnlineState::Offline
    );
    assert!(UpDeviceOnlineState::from_str("INVALID").is_err());
}

#[then(regex = r"^UpDeviceSleepState的Display和FromStr功能验证通过$")]
async fn verify_device_sleep_state(_world: &mut MyWorld) {
    // 测试 Display 功能
    assert_eq!(UpDeviceSleepState::Unknown.to_string(), "Unknown");
    assert_eq!(UpDeviceSleepState::Awake.to_string(), "Awake");
    assert_eq!(UpDeviceSleepState::Sleep.to_string(), "Sleep");

    // 测试 FromStr 功能
    assert_eq!(
        UpDeviceSleepState::from_str("UNKNOWN").unwrap(),
        UpDeviceSleepState::Unknown
    );
    assert_eq!(
        UpDeviceSleepState::from_str("AWAKE").unwrap(),
        UpDeviceSleepState::Awake
    );
    assert_eq!(
        UpDeviceSleepState::from_str("SLEEP").unwrap(),
        UpDeviceSleepState::Sleep
    );
    assert!(UpDeviceSleepState::from_str("INVALID").is_err());
}

#[then(regex = r"^UpDeviceOfflineCause的Display和FromStr功能验证通过$")]
async fn verify_device_offline_cause(_world: &mut MyWorld) {
    // 测试 Display 功能
    assert_eq!(UpDeviceOfflineCause::Unknown.to_string(), "Unknown");
    assert_eq!(UpDeviceOfflineCause::PowerOff.to_string(), "PowerOff");
    assert_eq!(
        UpDeviceOfflineCause::NetworkDisconnected.to_string(),
        "NetworkDisconnected"
    );
    assert_eq!(UpDeviceOfflineCause::DeviceError.to_string(), "DeviceError");
    assert_eq!(UpDeviceOfflineCause::Timeout.to_string(), "Timeout");

    // 测试 FromStr 功能
    assert_eq!(
        UpDeviceOfflineCause::from_str("UNKNOWN").unwrap(),
        UpDeviceOfflineCause::Unknown
    );
    assert_eq!(
        UpDeviceOfflineCause::from_str("POWER_OFF").unwrap(),
        UpDeviceOfflineCause::PowerOff
    );
    assert_eq!(
        UpDeviceOfflineCause::from_str("NETWORK_DISCONNECTED").unwrap(),
        UpDeviceOfflineCause::NetworkDisconnected
    );
    assert_eq!(
        UpDeviceOfflineCause::from_str("DEVICE_ERROR").unwrap(),
        UpDeviceOfflineCause::DeviceError
    );
    assert_eq!(
        UpDeviceOfflineCause::from_str("TIMEOUT").unwrap(),
        UpDeviceOfflineCause::Timeout
    );
    assert!(UpDeviceOfflineCause::from_str("INVALID").is_err());
}

#[then(regex = r"^设备类型ID工具类的功能验证通过$")]
async fn verify_type_ids_utility(_world: &mut MyWorld) {
    // 测试类型ID的基本验证功能
    let type_id = "test_type_id";
    assert!(!type_id.is_empty());
    assert!(type_id.len() > 0);

    // 测试空字符串
    let empty_type_id = "";
    assert!(empty_type_id.is_empty());
}

#[then(regex = r"^向量转换工具类的功能验证通过$")]
async fn verify_convert_vec_utility(_world: &mut MyWorld) {
    // 测试向量转换工具类的基本功能
    use logic_engine::device::device_caution::UpDeviceCaution;
    use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};

    // 测试 caution 转换功能
    let pair_list = UhsdPairList(vec![
        UhsdPair {
            name: "test1".to_string(),
            value: "value1".to_string(),
        },
        UhsdPair {
            name: "test2".to_string(),
            value: "value2".to_string(),
        },
    ]);

    let cautions = convert_vec::convert_caution_from(pair_list.clone());
    assert_eq!(cautions.len(), 2);
    assert_eq!(cautions[0].name(), "test1");
    assert_eq!(cautions[0].value(), "value1");

    let converted_back = convert_vec::convert_caution_to(cautions);
    assert_eq!(converted_back.0.len(), 2);
    assert_eq!(converted_back.0[0].name, "test1");
    assert_eq!(converted_back.0[0].value, "value1");
}

#[then(regex = r"^聚合设备的基本功能验证通过$")]
async fn verify_aggregate_device(_world: &mut MyWorld) {
    // 创建测试用的设备信息
    let device_info = create_test_device_info("aggregate_device_1", "聚合设备");
    let aggregate_device = UpAggregateDevice::new("test_unique_id".to_string(), device_info);

    // 验证基本功能
    assert_eq!(aggregate_device.device_id(), "aggregate_device_1");
    assert_eq!(aggregate_device.protocol(), "haier-usdk");
    assert!(!aggregate_device.get_extend_api().as_ref() as *const _ as usize == 0);

    // 验证配置状态
    let config_state = aggregate_device.get_config_state();
    assert!(matches!(
        config_state,
        UpDeviceConfigState::Unknown | UpDeviceConfigState::NativeDevice
    ));
}

#[then(regex = r"^通用设备的基本功能验证通过$")]
async fn verify_common_device(_world: &mut MyWorld) {
    // 创建测试用的设备信息
    let device_info = create_test_device_info("common_device_1", "通用设备");
    let common_device = UpCommonDevice::new("test_unique_id", device_info);

    // 验证基本功能
    assert_eq!(common_device.device_id(), "common_device_1");
    assert_eq!(common_device.protocol(), "haier-usdk");
    assert!(!common_device.get_extend_api().as_ref() as *const _ as usize == 0);

    // 验证配置状态
    let config_state = common_device.get_config_state();
    assert!(matches!(
        config_state,
        UpDeviceConfigState::Unknown | UpDeviceConfigState::NativeDevice
    ));
}

#[then(regex = r"^洗衣机设备的基本功能和配置状态验证通过$")]
async fn verify_washing_device(_world: &mut MyWorld) {
    // 创建测试用的设备信息
    let device_info = create_test_device_info("washing_device_1", "洗衣机");
    let washing_device = UpWashingDevice::new("test_unique_id".to_string(), device_info);

    // 验证基本功能
    assert_eq!(washing_device.device_id(), "washing_device_1");
    assert_eq!(washing_device.protocol(), "haier-usdk");
    assert!(!washing_device.get_extend_api().as_ref() as *const _ as usize == 0);

    // 验证配置状态
    let config_state = washing_device.get_config_state();
    assert!(matches!(
        config_state,
        UpDeviceConfigState::Unknown | UpDeviceConfigState::NativeDevice
    ));
}

#[then(regex = r"^设备过滤器功能验证通过$")]
async fn verify_device_filters(_world: &mut MyWorld) {
    // 创建测试设备
    let device1 = create_test_device("family_device_1", "family_001", None);
    let device2 = create_test_device("family_device_2", "family_002", None);
    let device3 = create_test_device("child_device_1", "family_001", Some("family_device_1"));

    // 测试家庭ID过滤器
    let family_filter = DeviceFilterByFamilyId::new("family_001".to_string());
    assert!(family_filter.accept(device1.as_ref()));
    assert!(!family_filter.accept(device2.as_ref()));
    assert!(family_filter.accept(device3.as_ref()));

    // 测试设备ID列表过滤器
    let device_ids = vec!["family_device_1".to_string(), "child_device_1".to_string()];
    let ids_filter = DeviceFilterByDeviceIds::new(device_ids);
    assert!(ids_filter.accept(device1.as_ref()));
    assert!(!ids_filter.accept(device2.as_ref()));
    assert!(ids_filter.accept(device3.as_ref()));

    // 测试父设备ID过滤器
    let parent_filter = DeviceFilterByParentId::new("family_device_1".to_string());
    assert!(!parent_filter.accept(device1.as_ref()));
    assert!(!parent_filter.accept(device2.as_ref()));
    assert!(parent_filter.accept(device3.as_ref()));
}

// 辅助函数
fn create_test_device_info(device_id: &str, display_name: &str) -> UpDeviceInfo {
    let extra = format!(
        r#"{{"DI-Basic.displayName":"{}","DI-Basic.room":"测试房间","DI-Basic.roomId":"room_001","DI-Basic.bindTime":"2024-01-01","DI-Product.brand":"海尔","DI-Product.bind_type":"wifi","DI-Product.configType":"test","DI-Product.category":"家电","DI-Relation.ownerId":"owner_001","DI-Product.imageAddr1":"http://image.url"}}"#,
        display_name
    );

    UpDeviceInfo::new(
        "haier-usdk".to_string(),
        device_id.to_string(),
        Some("fake_type_id1".to_string()),
        "fake_type_name1".to_string(),
        Some("fake_type_code1".to_string()),
        "fake_model1".to_string(),
        "fake_pro_no1".to_string(),
        Some("fake_parent_id1".to_string()),
        "fake_device_id2".to_string(),
        extra,
    )
}

fn create_test_device(
    device_id: &str,
    family_id: &str,
    parent_id: Option<&str>,
) -> Arc<dyn UpDevice> {
    let parent_id_str = parent_id.unwrap_or("");
    let extra = format!(
        r#"{{"DI-Relation.familyId":"{}","DI-Basic.displayName":"{}","DI-Basic.room":"测试房间","DI-Basic.roomId":"room_001"}}"#,
        family_id, device_id
    );

    let device_info = UpDeviceInfo::new(
        "haier-usdk".to_string(),
        device_id.to_string(),
        Some("fake_type_id1".to_string()),
        "fake_type_name1".to_string(),
        Some("fake_type_code1".to_string()),
        "fake_model1".to_string(),
        "fake_pro_no1".to_string(),
        if parent_id_str.is_empty() {
            None
        } else {
            Some(parent_id_str.to_string())
        },
        "fake_device_id2".to_string(),
        extra,
    );

    Arc::new(UpCommonDevice::new("test_unique_id", device_info))
}

// 这些步骤已经被新的简化版本替代，删除重复代码
