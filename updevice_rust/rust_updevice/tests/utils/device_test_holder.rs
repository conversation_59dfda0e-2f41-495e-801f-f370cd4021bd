use std::collections::{HashMap, VecDeque};
use std::result::Result as NormalResult;
use std::sync::Arc;

use logic_engine::data_source::config_data_source::{ConfigDataSource, ConfigDataSourceError};
use logic_engine::device::device_attribute::UpDeviceAttribute;
use logic_engine::device_config::business_attr::BusinessAttr;
use logic_engine::device_config::modifier::Modifier;
use logic_engine::engine::attribute::LEAttribute;
use logic_engine::engine::caution::LECaution;
use once_cell::sync::Lazy;
use parking_lot::RwLock;

use rust_updevice::api::device_filter::UpDeviceFilter;
use rust_updevice::api::device_injection::UpDeviceInjection;
use rust_updevice::api::error::Result;
use rust_updevice::api::event::{get_device_list_change_channel, UpDeviceEvent};
use rust_updevice::data_source::device_list_data_source::get_event_bus;
use rust_updevice::data_source::event::{DeviceDataSourceEvent, UserDataSourceEvent};
use rust_updevice::device::engine_device::UpEngineDevice;
use rust_updevice::device::up_device::UpDevice;
use rust_updevice::factory::device_factory::{DeviceFactory, UpDeviceFactory};
use rust_updevice::models::device_base_info::UpDeviceBaseInfo;
use rust_updevice::models::device_info::UpDeviceInfo;
use rust_updevice::utils::fn_clone::FnClone;
use rust_usdk::toolkit_ffi::uhsd_usr_model::{DeviceFeedback, DeviceInfo};
use rust_usdk::usdk_toolkit::error::ToolkitError;
use rust_usdk::usdk_toolkit::toolkit::get_event_bus as toolkit_get_event_bus;
use rust_usdk::usdk_toolkit::toolkit_event::{ToolkitEvent, CHANNEL_MESSAGE};
use rust_userdomain::api::user_domain::UserDomainState;

use crate::fake::fake_device::FakeDevice;
use crate::steps::engine_device_steps::RECEIVE_COMMANDS;
use crate::utils::step_utils::get_mock_device_toolkit_mut;
use rust_usdk::toolkit_ffi::uhsd_api::MockLibUHome;
use rust_usdk::toolkit_ffi::uhsd_injection::UhsdInjection;
use rust_usdk::toolkit_ffi::uhsd_manager::UhsdManager;

pub mod action {
    pub const PREPARE: &str = "prepare";
    pub const RELEASE: &str = "release";
    pub const NOTIFY_DEVICE_LIST_CHANGED: &str = "notify_device_list_changed";
    pub const NOTIFY_USER_INFO_CHANGE: &str = "notify_user_info_change";
    pub const UPDATE_DEVICE_LIST: &str = "update_device_list";
    pub const DEVICE_FILTER: &str = "device_filter";
    pub const GET_INITIAL_ATTRIBUTES: &str = "get_initial_attributes";
    pub const GET_ATTRIBUTES: &str = "get_attributes";
    pub const GET_ATTRIBUTE_BY_NAME: &str = "get_attribute_by_name";
    pub const GET_CAUTIONS: &str = "get_cautions";
    pub const GET_INITIAL_CAUTIONS: &str = "get_initial_cautions";
    pub const QUERY_ALARM_STATUS: &str = "query_alarm_status";
    pub const QUERY_BUSINESS_FUNCTIONS: &str = "query_business_functions";
    pub const QUERY_MODIFIER_CONFIGS: &str = "query_modifier_configs";
    pub const FAKE_GET_CONFIG_DATA_SOURCE: &str = "get_config_data_source";
}

pub mod commonly_used_key {
    pub const USDK_USER_ID_KEY: &str = "usdk-user-id";
    pub const USDK_ACCESS_TOKEN_KEY: &str = "usdk-access-token";
}

pub enum DataHolder {
    String(String),
    StringResult(NormalResult<String, ToolkitError>),
    Empty(Result<()>),
    DeviceList(Result<Vec<Arc<dyn UpDevice>>>),
    DeviceAttributeList(Result<Vec<UpDeviceAttribute>>),
    DeviceInfo(Result<DeviceInfo>),
    CommandResult(Result<DeviceFeedback>),
    CommandResultMap(HashMap<String, Result<DeviceFeedback>>),
    ToolkitEvent(ToolkitEvent),
    CautionList(Vec<LECaution>),
    AttributeList(Vec<Arc<LEAttribute>>),
    Attribute(Option<Arc<LEAttribute>>),
    BusinessFunctionsList(Vec<BusinessAttr>),
    ModifierList(Vec<Modifier>),
    ConfigDataSource(NormalResult<String, ConfigDataSourceError>),
    Bool(bool),
}

pub enum ParamHolder {
    String(String),
    Map(HashMap<String, String>),
    DeviceList(Vec<Arc<dyn UpDevice>>),
    FilterList(Vec<Option<Box<dyn UpDeviceFilter>>>),
}
pub struct UpDeviceTestHolder {
    device_factory: Option<Box<dyn UpDeviceFactory>>,
    real_device_factory: Option<DeviceFactory>,
    device_listener_capture_map: RwLock<HashMap<String, Vec<String>>>,
    engine_device: Option<UpEngineDevice>,
    param_map: HashMap<String, ParamHolder>,
    result_map: HashMap<String, DataHolder>,
    notify_times_map: HashMap<String, usize>,
    notify_param_map: HashMap<String, ParamHolder>,
    subscription_id_map: HashMap<String, Vec<String>>,
    fake_device: Option<FakeDevice>,
    device_data_source_listener: Option<FnClone<DeviceDataSourceEvent>>,
    expect_remote_device_list_result: VecDeque<Result<Vec<UpDeviceInfo>>>,
    expect_cache_device_list_result: VecDeque<Vec<UpDeviceInfo>>,
    is_mock_device_toolkit: bool,
    config_data_source: Option<Box<dyn ConfigDataSource>>,
    engine_device_mock_extend_api: Option<UpEngineDevice>,
    user_domain_state: UserDomainState,
    user_domain_data_source: Option<FnClone<UserDataSourceEvent>>,
    subscription_event_map: HashMap<String, Vec<UpDeviceEvent>>,
    unsubscribe_id_map: HashMap<String, Vec<String>>,
    user_domain_uhome_access_token: String,
    user_domain_access_token: String,
    user_domain_user_id: String,
    test_devices: HashMap<String, Arc<dyn UpDevice>>,
    test_states: HashMap<String, String>,
    test_filters: HashMap<String, Box<dyn UpDeviceFilter>>,
}

static mut INSTANCE: Lazy<UpDeviceTestHolder> = Lazy::new(UpDeviceTestHolder::new);

impl UpDeviceTestHolder {
    pub fn get_instance() -> &'static mut Self {
        unsafe { &mut INSTANCE }
    }

    fn new() -> Self {
        UpDeviceTestHolder {
            device_factory: None,
            real_device_factory: None,
            device_listener_capture_map: RwLock::new(HashMap::new()),
            engine_device: None,
            param_map: HashMap::new(),
            result_map: HashMap::new(),
            notify_times_map: HashMap::new(),
            notify_param_map: HashMap::new(),
            subscription_id_map: HashMap::new(),
            fake_device: None,
            device_data_source_listener: None,
            expect_remote_device_list_result: VecDeque::new(),
            expect_cache_device_list_result: VecDeque::new(),
            is_mock_device_toolkit: true,
            config_data_source: None,
            engine_device_mock_extend_api: None,
            user_domain_state: UserDomainState::UnLogin,
            user_domain_data_source: None,
            subscription_event_map: HashMap::new(),
            unsubscribe_id_map: HashMap::new(),
            user_domain_uhome_access_token: "".to_string(),
            user_domain_access_token: "".to_string(),
            user_domain_user_id: "".to_string(),
            test_devices: HashMap::new(),
            test_states: HashMap::new(),
            test_filters: HashMap::new(),
        }
    }

    pub fn create_device(
        &mut self,
        device_type: String,
        unique_id: String,
        device_info: UpDeviceBaseInfo,
    ) -> Option<UpDeviceBaseInfo> {
        match device_type.as_str() {
            "测试" => {
                let device_base_info = UpDeviceBaseInfo::new(
                    device_info.protocol().clone(),
                    device_info.device_id().clone(),
                    device_info.type_id().clone(),
                    device_info.type_code().clone(),
                    device_info.model().clone(),
                    device_info.product_code().clone(),
                    device_info.parent_id().clone(),
                    device_info.sub_device_id().clone(),
                );

                Some(device_base_info)
            }
            "逻辑引擎" => {
                let engine_device = UpEngineDevice::new(
                    unique_id.as_str(),
                    UpDeviceInfo::init_with_base_info(device_info.clone()),
                );
                self.engine_device = Some(engine_device);
                Some(device_info)
            }
            _ => None,
        }
    }
    pub fn get_user_domain_access_token(&self) -> String {
        self.user_domain_access_token.clone()
    }
    pub fn set_user_domain_access_token(&mut self, token: String) {
        self.user_domain_access_token = token;
    }
    pub fn get_user_domain_uhome_access_token(&self) -> String {
        self.user_domain_uhome_access_token.clone()
    }
    pub fn set_user_domain_uhome_access_token(&mut self, token: String) {
        self.user_domain_uhome_access_token = token;
    }
    pub fn get_user_domain_state(&self) -> UserDomainState {
        self.user_domain_state
    }

    pub fn set_user_domain_state(&mut self, state: UserDomainState) {
        self.user_domain_state = state;
    }

    pub fn set_mock_device_toolkit(&mut self, is_mock_device_toolkit: bool) {
        self.is_mock_device_toolkit = is_mock_device_toolkit;
    }

    pub fn set_device_data_source_listener(&mut self, listener: FnClone<DeviceDataSourceEvent>) {
        self.device_data_source_listener = Some(listener);
    }

    pub fn set_config_data_source(&mut self, config_data_source: Box<dyn ConfigDataSource>) {
        self.config_data_source = Some(config_data_source);
    }

    pub fn get_device_data_source_listener(&self) -> Option<FnClone<DeviceDataSourceEvent>> {
        self.device_data_source_listener.clone()
    }
    pub fn set_user_data_source_listener(&mut self, listener: FnClone<UserDataSourceEvent>) {
        self.user_domain_data_source = Some(listener);
    }
    pub fn get_user_data_source_listener(&self) -> Option<FnClone<UserDataSourceEvent>> {
        self.user_domain_data_source.clone()
    }
    pub fn get_device_factory(&self) -> Option<&Box<dyn UpDeviceFactory>> {
        self.device_factory.as_ref()
    }

    pub fn set_device_factory(&mut self, device_factory: Box<dyn UpDeviceFactory>) {
        self.device_factory = Some(device_factory);
    }

    pub fn set_real_device_factory(&mut self, device_factory: DeviceFactory) {
        self.real_device_factory = Some(device_factory);
    }

    pub fn get_real_device_factory(&self) -> &DeviceFactory {
        &self
            .real_device_factory
            .as_ref()
            .expect("real_device_factory is None")
    }

    pub fn push_device_into_data_source(&self, device_base_info: Option<UpDeviceBaseInfo>) {
        if let Some(device_base_info) = device_base_info {
            let mut device_info = UpDeviceInfo::empty();
            device_info.update_base_info(device_base_info);
            get_event_bus().publish(
                get_device_list_change_channel().as_str(),
                DeviceDataSourceEvent::DeviceListChanged(vec![device_info]),
            );
        }
    }

    pub fn capture_device_event_for_listener(
        &mut self,
        user_name: String,
        event_type_name: String,
    ) {
        let device_listener_capture_map = self.device_listener_capture_map.read();
        let entry = device_listener_capture_map.get(&user_name);
        if let Some(entry) = entry {
            let mut list = entry.clone();
            list.push(event_type_name);
            drop(device_listener_capture_map);
            let mut device_listener_capture_map = self.device_listener_capture_map.write();
            device_listener_capture_map.insert(user_name, list);
        } else {
            let mut list = Vec::new();
            list.push(event_type_name);
            drop(device_listener_capture_map);
            let mut device_listener_capture_map = self.device_listener_capture_map.write();
            device_listener_capture_map.insert(user_name, list);
        }
    }

    pub fn get_received_device_event_list_by(&self, user_name: String) -> Vec<String> {
        let device_listener_capture_map = self.device_listener_capture_map.read();
        let entry = device_listener_capture_map.get(&user_name);
        if let Some(entry) = entry {
            entry.clone()
        } else {
            vec![]
        }
    }
    pub fn get_engine_device(&self) -> &UpEngineDevice {
        println!("get_engine_device");
        &self.engine_device.as_ref().expect("engine_device is None")
    }

    pub fn set_param(&mut self, key: &str, param: ParamHolder) {
        self.param_map.insert(key.to_string(), param);
    }

    pub fn get_param(&self, key: &str) -> Option<&ParamHolder> {
        self.param_map.get(key)
    }

    pub fn pop_param(&mut self, key: &str) -> Option<ParamHolder> {
        self.param_map.remove(key)
    }

    pub fn get_param_mut(&mut self, key: &str) -> Option<&mut ParamHolder> {
        self.param_map.get_mut(key)
    }

    pub fn set_result(&mut self, key: &str, result: DataHolder) {
        self.result_map.insert(key.to_string(), result);
    }

    pub fn get_result(&self, key: &str) -> &DataHolder {
        self.result_map.get(key).expect("result is None")
    }

    pub fn get_result_mut(&mut self, key: &str) -> &mut DataHolder {
        self.result_map
            .entry(key.to_string())
            .or_insert(DataHolder::Empty(Ok(())))
    }

    pub fn expect_remote_device_list_push_back(&mut self, result: Result<Vec<UpDeviceInfo>>) {
        self.expect_remote_device_list_result.push_back(result);
    }

    pub fn expect_remote_device_list_pop_front(&mut self) -> Result<Vec<UpDeviceInfo>> {
        self.expect_remote_device_list_result
            .pop_front()
            .expect("expect_device_info_result is None")
    }

    pub fn expect_cache_device_list_push_back(&mut self, device_list: Vec<UpDeviceInfo>) {
        self.expect_cache_device_list_result.push_back(device_list);
    }

    pub fn clean_expect_cache_device_list(&mut self) {
        self.expect_cache_device_list_result.clear();
    }

    pub fn expect_cache_device_list_pop_front(&mut self) -> Vec<UpDeviceInfo> {
        self.expect_cache_device_list_result
            .pop_front()
            .expect("expect_device_info_result is None")
    }

    pub fn increment_notify_times(&mut self, key: &str) {
        self.notify_times_map
            .entry(key.to_string())
            .and_modify(|val| *val += 1)
            .or_insert(1);
    }
    pub fn get_notify_times(&self, key: &str) -> usize {
        self.notify_times_map.get(key).cloned().unwrap_or(0)
    }

    pub fn set_notify_param(&mut self, key: &str, param: ParamHolder) {
        self.notify_param_map.insert(key.to_string(), param);
    }

    pub fn notify_param_is_empty(&self, key: &str) -> bool {
        self.notify_param_map.get(key).is_none()
    }

    pub fn get_user_domain_user_id(&self) -> String {
        self.user_domain_user_id.clone()
    }

    pub fn set_user_domain_user_id(&mut self, user_id: String) {
        self.user_domain_user_id = user_id;
    }

    pub fn get_notify_param(&self, key: &str) -> &ParamHolder {
        self.notify_param_map
            .get(key)
            .expect("notify_param is None")
    }
    pub fn record_subscription_id(&mut self, channel: &str, subscription_id: &str) {
        self.subscription_id_map
            .entry(channel.to_string())
            .and_modify(|val| val.push(subscription_id.to_string()))
            .or_insert(vec![subscription_id.to_string()]);
    }

    pub fn get_subscription_id(&self, channel: &str) -> Vec<String> {
        self.subscription_id_map
            .get(channel)
            .cloned()
            .unwrap_or_default()
    }

    pub fn record_subscription_event(&mut self, channel: &str, event: UpDeviceEvent) {
        self.subscription_event_map
            .entry(channel.to_string())
            .and_modify(|val| val.push(event.clone()))
            .or_insert(vec![event.clone()]);
    }

    pub fn get_subscription_event(&self, channel: &str) -> Vec<UpDeviceEvent> {
        self.subscription_event_map
            .get(channel)
            .cloned()
            .unwrap_or_default()
    }

    pub fn clear_subscription_event(&mut self, channel: &str) {
        self.subscription_event_map.remove(channel);
    }
    pub fn clear(&mut self) {
        self.device_factory = None;
        self.real_device_factory = None;
        self.device_listener_capture_map.write().clear();
        self.engine_device = None;
        self.param_map.clear();
        self.result_map.clear();
        self.notify_times_map.clear();
        self.notify_param_map.clear();
        self.unsubscribe();
        self.clear_devices();
        self.expect_remote_device_list_result.clear();
        self.expect_cache_device_list_result.clear();
        self.is_mock_device_toolkit = true;
        self.config_data_source = None;
        self.user_domain_state = UserDomainState::UnLogin;
        self.subscription_event_map.clear();
        self.subscription_id_map.clear();
        for (key, value) in self.unsubscribe_id_map.iter() {
            for id in value {
                UpDeviceInjection::get_instance()
                    .get_event_bus()
                    .remove(key, id);
            }
        }
        self.unsubscribe_id_map.clear();
        let mut receive_commands = RECEIVE_COMMANDS.write();
        receive_commands.clear();

        // 清理测试相关的数据
        self.test_devices.clear();
        self.test_states.clear();
        self.test_filters.clear();

        // 重置 UhsdManager 的状态，确保每个测试场景都有干净的初始化状态
        self.reset_uhsd_manager();
    }

    fn clear_devices(&self) {
        if self.is_mock_device_toolkit {
            let mock_device_toolkit = get_mock_device_toolkit_mut();
            mock_device_toolkit
                .mock_unsubscribe_device_change(mry::Any)
                .returns_with(|_| Ok(()));
            UpDeviceInjection::get_instance()
                .get_device_manager_mut()
                .clear_device_list();
            UpDeviceInjection::get_instance()
                .get_device_manager()
                .prepend_device_factory(Box::new(DeviceFactory::new()));
            toolkit_get_event_bus().publish(CHANNEL_MESSAGE, ToolkitEvent::Disconnected);
        }
    }
    fn unsubscribe(&mut self) {
        let device_list_change_listeners = self
            .subscription_id_map
            .remove(get_device_list_change_channel().as_str());
        if let Some(listeners) = device_list_change_listeners {
            for subscription_id in listeners {
                UpDeviceInjection::get_instance()
                    .get_device_manager()
                    .unsubscribe_device_list_change(&subscription_id);
            }
        }
    }

    pub fn set_engine_device(&mut self, device: UpEngineDevice) {
        self.engine_device = Some(device);
    }

    pub fn set_engine_device_mock_extend_api(&mut self, device: UpEngineDevice) {
        self.engine_device_mock_extend_api = Some(device);
    }

    pub fn get_engine_device_mock_extend_api(&mut self) -> &mut UpEngineDevice {
        self.engine_device_mock_extend_api
            .as_mut()
            .expect("engine_device_mock_extend_api is None")
    }

    pub fn get_fake_device(&self) -> &FakeDevice {
        self.fake_device.as_ref().expect("fake_device is None")
    }

    pub fn set_fake_device(&mut self, fake_device: FakeDevice) {
        self.fake_device = Some(fake_device);
    }

    pub fn record_unsubscribe_id(&mut self, channel: &str, subscription_id: &str) {
        self.unsubscribe_id_map
            .entry(channel.to_string())
            .and_modify(|val| val.push(subscription_id.to_string()))
            .or_insert(vec![subscription_id.to_string()]);
    }

    /// 重置 UhsdManager 的状态，确保每个测试场景都有干净的初始化状态
    fn reset_uhsd_manager(&self) {
        // 重置 UhsdManager 中的所有 OnceCell
        UhsdManager::get_instance().reset_for_test();

        // 重新设置 MockLibUHome
        UhsdInjection::get_instance().set_lib_uhome(Box::new(MockLibUHome::default()));
    }

    // 测试设备管理方法
    pub fn set_test_device(&mut self, key: &str, device: Arc<dyn UpDevice>) {
        self.test_devices.insert(key.to_string(), device);
    }

    pub fn get_test_device(&self, key: &str) -> Option<Arc<dyn UpDevice>> {
        self.test_devices.get(key).cloned()
    }

    pub fn remove_test_device(&mut self, key: &str) -> Option<Arc<dyn UpDevice>> {
        self.test_devices.remove(key)
    }

    // 测试状态管理方法
    pub fn set_test_state(&mut self, key: &str, value: &str) {
        self.test_states.insert(key.to_string(), value.to_string());
    }

    pub fn get_test_state(&self, key: &str) -> Option<String> {
        self.test_states.get(key).cloned()
    }

    pub fn remove_test_state(&mut self, key: &str) -> Option<String> {
        self.test_states.remove(key)
    }

    // 测试过滤器管理方法
    pub fn set_test_filter(&mut self, key: &str, filter: Box<dyn UpDeviceFilter>) {
        self.test_filters.insert(key.to_string(), filter);
    }

    pub fn get_test_filter(&self, key: &str) -> Option<&Box<dyn UpDeviceFilter>> {
        self.test_filters.get(key)
    }

    pub fn remove_test_filter(&mut self, key: &str) -> Option<Box<dyn UpDeviceFilter>> {
        self.test_filters.remove(key)
    }
}
