Feature: 设备模型和工具类测试

  Background:
    Given 初始化设备管理器

  Scenario: [1032]聚合设备创建和基本功能正常
    Given 使用者创建"测试"设备工厂
    Given 创建"测试"设备,唯一标识ID为"aggregate_test",设备信息如下:
      | Protocol   | DeviceId           | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                          |
      | haier-usdk | aggregate_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"聚合设备","DI-Basic.room":"客厅","DI-Basic.roomId":"room_001","DI-Basic.bindTime":"2024-01-01","DI-Product.brand":"海尔","DI-Product.bind_type":"wifi","DI-Product.configType":"aggregate","DI-Product.category":"家电","DI-Relation.ownerId":"owner_001","DI-Product.imageAddr1":"http://image.url"} |
    Then 聚合设备的基本功能验证通过

  Scenario: [1033]通用设备创建和基本功能正常
    Given 使用者创建"测试"设备工厂
    Given 创建"测试"设备,唯一标识ID为"common_test",设备信息如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                       |
      | haier-usdk | common_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"通用设备","DI-Basic.room":"卧室","DI-Basic.roomId":"room_002","DI-Basic.bindTime":"2024-01-01","DI-Product.brand":"海尔","DI-Product.bind_type":"wifi","DI-Product.configType":"common","DI-Product.category":"家电","DI-Relation.ownerId":"owner_001","DI-Product.imageAddr1":"http://image.url"} |
    Then 通用设备的基本功能验证通过

  Scenario: [1034]洗衣机设备创建和配置状态功能正常
    Given 使用者创建"测试"设备工厂
    Given 创建"测试"设备,唯一标识ID为"washing_test",设备信息如下:
      | Protocol   | DeviceId         | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                        |
      | haier-usdk | washing_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"洗衣机","DI-Basic.room":"阳台","DI-Basic.roomId":"room_003","DI-Basic.bindTime":"2024-01-01","DI-Product.brand":"海尔","DI-Product.bind_type":"wifi","DI-Product.configType":"washing","DI-Product.category":"洗衣机","DI-Relation.ownerId":"owner_001","DI-Product.imageAddr1":"http://image.url"} |
    Then 洗衣机设备的基本功能和配置状态验证通过

  Scenario: [1035]设备过滤器功能正常
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                        |
      | haier-usdk | family_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Relation.familyId":"family_001","DI-Basic.displayName":"家庭设备1","DI-Basic.room":"客厅","DI-Basic.roomId":"room_001"} |
      | haier-usdk | family_device_2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Relation.familyId":"family_002","DI-Basic.displayName":"家庭设备2","DI-Basic.room":"卧室","DI-Basic.roomId":"room_002"} |
      | haier-usdk | child_device_1  | fake_type_id3 | fake_type_name3 | fake_type_code3 | fake_model3 | fake_pro_no3 | family_device_1 | fake_device_id4 | {"DI-Relation.familyId":"family_001","DI-Basic.displayName":"子设备1","DI-Basic.room":"厨房","DI-Basic.roomId":"room_003"}   |
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Then 设备过滤器功能验证通过

  # 用户数据源测试
  Scenario: [3011]用户数据源订阅用户信息变化测试
    Given 用户数据源初始化完成
    When 订阅者"userA"订阅用户信息变化
    When 用户域上报token无效事件
    Then 订阅者"userA"收到用户信息变化事件"Logout"

  Scenario: [3012]用户数据源订阅用户信息变化-退出登录测试
    Given 用户数据源初始化完成
    When 订阅者"userB"订阅用户信息变化
    When 用户域上报退出登录事件
    Then 订阅者"userB"收到用户信息变化事件"Logout"

  Scenario: [3013]用户数据源取消订阅测试
    Given 用户数据源初始化完成
    When 订阅者"userC"订阅用户信息变化
    When 订阅者"userC"取消订阅用户信息变化
    When 用户域上报token无效事件
    Then 订阅者"userC"未收到用户信息变化事件

  # 设备列表数据源测试
  Scenario: [3014]设备列表数据源获取远程设备列表成功测试
    Given 设备列表数据源初始化完成
    Given 用户域获取设备列表接口返回成功,设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                |
      | haier-usdk | remote_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"远程设备1"} |
      | haier-usdk | remote_device_2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"远程设备2"} |
    When 调用设备列表数据源的获取远程设备列表接口
    Then 获取远程设备列表接口返回成功,设备数量为"2"
    Then 获取远程设备列表接口返回的设备ID包含"remote_device_1,remote_device_2"

  Scenario: [3015]设备列表数据源获取远程设备列表失败测试
    Given 设备列表数据源初始化完成
    Given 用户域获取设备列表接口返回失败,错误信息为"网络连接失败"
    When 调用设备列表数据源的获取远程设备列表接口
    Then 获取远程设备列表接口返回失败,错误信息包含"网络连接失败"

  Scenario: [3016]设备列表数据源获取缓存设备列表测试
    Given 设备列表数据源初始化完成
    Given 设备列表数据源缓存中有设备列表如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                |
      | haier-usdk | cached_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"缓存设备1"} |
    When 调用设备列表数据源的获取缓存设备列表接口
    Then 获取缓存设备列表接口返回设备数量为"1"
    Then 获取缓存设备列表接口返回的设备ID包含"cached_device_1"

  Scenario: [3017]设备列表数据源更新缓存设备列表测试
    Given 设备列表数据源初始化完成
    Given 新的设备列表如下:
      | Protocol   | DeviceId     | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                              |
      | haier-usdk | new_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"新设备1"} |
      | haier-usdk | new_device_2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"新设备2"} |
    When 调用设备列表数据源的更新缓存设备列表接口
    Then 更新缓存设备列表接口返回成功
    When 调用设备列表数据源的获取缓存设备列表接口
    Then 获取缓存设备列表接口返回设备数量为"2"
    Then 获取缓存设备列表接口返回的设备ID包含"new_device_1,new_device_2"

  # 事件系统测试
  Scenario: [3018]设备变化事件通道测试
    Given 事件系统初始化完成
    When 获取设备变化事件通道
    Then 设备变化事件通道不为空
    Then 设备变化事件通道的通道名称包含"test_device_id_change_channel"

  Scenario: [3019]设备列表变化事件通道测试
    Given 事件系统初始化完成
    When 获取设备列表变化事件通道
    Then 设备列表变化事件通道不为空
    Then 设备列表变化事件通道的通道名称包含"device_list_change"

  # 工具函数测试
  Scenario: [3020]向量转换工具测试
    Given 字符串向量包含"item1,item2,item3"
    When 使用向量转换工具转换为字符串
    Then 转换后的字符串为"item1,item2,item3"
    When 使用向量转换工具转换回向量
    Then 转换后的向量包含"item1,item2,item3"

  Scenario: [3021]空向量转换工具测试
    Given 空字符串向量
    When 使用向量转换工具转换为字符串
    Then 转换后的字符串为空
    When 使用向量转换工具转换回向量
    Then 转换后的向量为空
