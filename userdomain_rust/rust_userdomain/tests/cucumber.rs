use std::time::Duration;

use cucumber::World;
use futures::FutureExt;
use tokio::time;

use crate::utils::steps_utils::{clear_all_event_record, clear_maps};

mod fake;
mod steps;
mod utils;

#[derive(Debug, Default, World)]
pub struct MyWorld;

impl MyWorld {
    pub fn new() -> Self {
        MyWorld {}
    }
}

#[tokio::main]
async fn main() {
    std::env::set_var("CARGO_TEST", "1");
    MyWorld::cucumber()
        .max_concurrent_scenarios(1)
        .before(|_feature, _rule, _scenario, _world| {
            clear_all_event_record();
            clear_maps();
            time::sleep(Duration::from_millis(1)).boxed_local()
        })
        .after(|_feature, _gherkin, _rule, _scenario, _world| {
            time::sleep(Duration::from_millis(1)).boxed_local()
        })
        .run_and_exit("tests/features/baseDevice.feature")
        .await;
    // .filter_run("tests/features", |_, _, sc| {
    //     sc.tags.iter().any(|t| t == "debug")
    // })
    // .await;
}
