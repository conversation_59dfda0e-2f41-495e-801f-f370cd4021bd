# Rust 项目单元测试覆盖率分析报告生成提示词模板

## 📋 使用说明

本模板用于指导 AI 助手生成 Rust 项目的单元测试覆盖率分析报告。请根据具体项目情况调整模板中的参数。

## 🎯 提示词模板

```
请为 {项目名称} 项目生成单元测试覆盖率分析报告，要求如下：

### 1. 执行覆盖率测试
- 使用 `cargo llvm-cov --test {测试类型} --html` 生成 HTML 覆盖率报告
- 使用 `cargo llvm-cov --test {测试类型} --summary-only` 获取文本格式覆盖率数据
- 只统计 {项目路径} 目录下的代码，排除其他依赖库

### 2. 数据筛选要求
- 从覆盖率输出中筛选出以 "{项目路径}/src" 开头的行
- 提取区域覆盖率、函数覆盖率、行覆盖率三个维度的数据
- 记录测试执行结果：特性数量、场景数量、步骤数量、通过率

### 3. 报告结构要求
按照以下结构生成报告：

#### 3.1 总体覆盖率统计
按模块功能分类统计，包含以下分类：
- 核心模块覆盖率
- {业务模块1}覆盖率  
- {业务模块2}覆盖率
- 数据源模块覆盖率
- 工具类模块覆盖率

每个分类使用表格格式：
| 模块 | 区域覆盖率 | 函数覆盖率 | 行覆盖率 | 说明 |
|------|------------|------------|----------|------|

#### 3.2 测试执行结果
- 测试特性数量
- 测试场景数量  
- 测试步骤数量
- 执行结果状态

#### 3.3 覆盖率分析
分为三个层次：
- 高覆盖率模块 (>90%)
- 中等覆盖率模块 (70%-90%)
- 需要改进的模块 (<70%)

#### 3.4 建议
针对低覆盖率模块提出具体改进建议

#### 3.5 总结
分析项目测试覆盖率的整体情况，包括优势和待改进方面

### 4. 输出要求
- 将报告保存为 `{项目路径}/tests/docs/{项目名称}单测覆盖率分析报告.md`
- 使用中文撰写报告
- 数据要准确，基于实际的覆盖率测试结果
- 格式要规范，便于阅读和维护

### 5. 注意事项
- 只统计项目本身的代码，不包括第三方依赖
- 确保覆盖率数据的准确性
- 提供具体可行的改进建议
- 保持报告格式的一致性
```

## 🔧 模板参数说明

### 必填参数
- `{项目名称}`: 项目的名称，如 "rust_updevice"
- `{测试类型}`: 测试类型，如 "cucumber"、"unit"、"integration"
- `{项目路径}`: 项目在文件系统中的路径，如 "updevice_rust/rust_updevice"

### 可选参数
- `{业务模块1}`, `{业务模块2}`: 根据项目实际情况定义的业务模块分类
- 覆盖率阈值可根据项目要求调整 (当前为 90% 和 70%)

## 💡 使用示例

### 示例1: UPDevice 项目
```
请为 rust_updevice 项目生成单元测试覆盖率分析报告，要求如下：

### 1. 执行覆盖率测试
- 使用 `cargo llvm-cov --test cucumber --html` 生成 HTML 覆盖率报告
- 使用 `cargo llvm-cov --test cucumber --summary-only` 获取文本格式覆盖率数据
- 只统计 updevice_rust/rust_updevice 目录下的代码，排除其他依赖库

### 2. 数据筛选要求
- 从覆盖率输出中筛选出以 "updevice_rust/rust_updevice/src" 开头的行
- 提取区域覆盖率、函数覆盖率、行覆盖率三个维度的数据
- 记录测试执行结果：特性数量、场景数量、步骤数量、通过率

### 3. 报告结构要求
按照以下结构生成报告：

#### 3.1 总体覆盖率统计
按模块功能分类统计，包含以下分类：
- 核心模块覆盖率
- 设备兼容性模块覆盖率  
- 工厂模式模块覆盖率
- 数据模型覆盖率
- 守护进程模块覆盖率
- 数据源模块覆盖率
- 设备状态模块覆盖率
- 工具类模块覆盖率

[其余部分按模板执行...]
```

## 📚 常用命令参考

### 覆盖率测试命令
```bash
# 生成 HTML 覆盖率报告
cargo llvm-cov --test cucumber --html

# 生成文本格式覆盖率摘要
cargo llvm-cov --test cucumber --summary-only

# 只针对特定模块生成覆盖率
cargo llvm-cov --test cucumber --summary-only | grep "项目路径/src"

# 清理之前的覆盖率数据
cargo llvm-cov clean
```

### 数据提取命令
```bash
# 提取项目覆盖率数据
cargo llvm-cov --test cucumber --summary-only | grep "updevice_rust/rust_updevice/src"

# 统计测试执行结果
cargo test --test cucumber 2>&1 | tail -10
```

## ✅ 质量检查清单

在生成报告前，请确认以下事项：

### 数据准确性
- [ ] 覆盖率数据来源于实际测试执行
- [ ] 已排除第三方依赖库的统计
- [ ] 测试执行结果真实有效
- [ ] 数据计算无误差

### 报告完整性  
- [ ] 包含所有必要的模块分类
- [ ] 覆盖率分析层次清晰
- [ ] 改进建议具体可行
- [ ] 总结客观准确

### 格式规范性
- [ ] 表格格式正确
- [ ] 百分比数据精确到小数点后2位
- [ ] 中文表述规范
- [ ] Markdown 语法正确

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 覆盖率数据为空
**问题**: 执行覆盖率命令后没有数据输出
**解决**: 
- 检查测试是否能正常运行
- 确认 llvm-cov 工具已正确安装
- 清理缓存后重新执行

#### 2. 数据包含其他库
**问题**: 覆盖率统计包含了依赖库的代码
**解决**:
- 使用 grep 过滤特定路径
- 检查项目结构是否正确
- 确认筛选条件准确

#### 3. 报告格式错误
**问题**: 生成的报告格式不符合要求
**解决**:
- 检查模板参数是否正确填写
- 确认表格语法正确
- 验证 Markdown 格式

## 🔗 版本历史

- v1.0 (2024-12): 初始版本，支持基本的覆盖率分析报告生成
